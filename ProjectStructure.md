# GoAssistant Project Structure

## Directory Layout

```
goassistant/
├── cmd/
│   └── assistant/
│       └── main.go                 # Application entry point
│
├── internal/                       # Private application code
│   ├── assistant/                  # Core assistant logic
│   │   ├── assistant.go           # Main assistant interface and implementation
│   │   ├── context.go             # Conversation context management
│   │   ├── processor.go           # Request processing pipeline
│   │   └── errors.go              # Domain-specific errors
│   │
│   ├── ai/                        # AI provider integrations
│   │   ├── provider.go            # Provider interface definition
│   │   ├── claude/                # Claude implementation
│   │   ├── gemini/                # Gemini implementation
│   │   └── embeddings/            # Embedding services
│   │
│   ├── agents/                    # LangChain agent implementations
│   │   ├── agent.go               # Base agent interface
│   │   ├── development/           # Development assistant agent
│   │   ├── database/              # Database expert agent
│   │   ├── infrastructure/        # K8s/Docker agent
│   │   ├── research/              # Research agent
│   │   └── executor.go            # Agent executor wrapper
│   │
│   ├── chains/                    # LangChain chain implementations
│   │   ├── chain.go               # Base chain interface
│   │   ├── conversation.go        # Conversational chains
│   │   ├── retrieval.go           # RAG chains
│   │   ├── sql.go                 # Database query chains
│   │   └── code.go                # Code generation chains
│   │
│   ├── memory/                    # Memory implementations
│   │   ├── memory.go              # Memory interface
│   │   ├── buffer.go              # Conversation buffer memory
│   │   ├── vector.go              # Vector store memory
│   │   ├── summary.go             # Conversation summarization
│   │   └── postgres.go            # PostgreSQL-backed memory
│   │
│   ├── tools/                     # Tool implementations
│   │   ├── registry.go            # Tool registry and interface
│   │   ├── adapter.go             # LangChain tool adapter
│   │   ├── search/                # Web search tool
│   │   │   ├── searxng.go         # SearXNG implementation
│   │   │   └── langchain.go      # LangChain wrapper
│   │   ├── godev/                 # Go development tools
│   │   │   ├── analyzer.go        # AST analyzer
│   │   │   ├── profiler.go        # pprof integration
│   │   │   ├── tracer.go          # Execution tracer
│   │   │   └── langchain.go      # LangChain wrappers
│   │   ├── postgres/              # PostgreSQL tools
│   │   │   ├── executor.go        # Query executor
│   │   │   ├── analyzer.go        # Query analyzer
│   │   │   ├── schema.go          # Schema explorer
│   │   │   └── langchain.go      # LangChain wrappers
│   │   ├── k8s/                   # Kubernetes tools
│   │   │   ├── client.go          # K8s client wrapper
│   │   │   ├── resources.go       # Resource management
│   │   │   └── langchain.go      # LangChain wrappers
│   │   ├── docker/                # Docker tools
│   │   │   ├── client.go          # Docker client wrapper
│   │   │   ├── containers.go      # Container management
│   │   │   └── langchain.go      # LangChain wrappers
│   │   ├── cloudflare/            # Cloudflare tools
│   │   │   ├── client.go          # CF API client
│   │   │   ├── tunnel.go          # Tunnel management
│   │   │   ├── r2.go              # R2 storage
│   │   │   └── langchain.go      # LangChain wrappers
│   │   └── langchain/             # Pure LangChain tools
│   │       ├── calculator.go      # Math calculations
│   │       ├── python.go          # Python REPL
│   │       └── custom.go          # Custom tool builder
│   │
│   ├── storage/                   # Data persistence layer
│   │   ├── postgres/              # PostgreSQL implementation
│   │   │   ├── client.go          # Database client
│   │   │   ├── migrations/        # SQL migrations
│   │   │   ├── queries/           # SQL queries (for sqlc)
│   │   │   └── models.go          # Database models
│   │   └── cache/                 # Caching layer
│   │
│   ├── server/                    # HTTP server
│   │   ├── server.go              # Server setup
│   │   ├── routes.go              # Route definitions
│   │   ├── middleware/            # HTTP middleware
│   │   ├── handlers/              # HTTP handlers
│   │   └── ws/                    # WebSocket handlers
│   │
│   ├── cli/                       # CLI implementation
│   │   ├── cli.go                 # CLI setup
│   │   ├── commands/              # CLI commands
│   │   └── interactive/           # Interactive mode
│   │
│   ├── web/                       # Web UI assets
│   │   ├── templates/             # Templ templates
│   │   ├── static/                # Static assets
│   │   └── components/            # Reusable UI components
│   │
│   ├── config/                    # Configuration
│   │   ├── config.go              # Configuration structure
│   │   ├── loader.go              # Configuration loading
│   │   └── validator.go           # Configuration validation
│   │
│   └── observability/             # Monitoring and tracing
│       ├── metrics.go             # Prometheus metrics
│       ├── tracing.go             # OpenTelemetry setup
│       └── logging.go             # Structured logging setup
│
├── pkg/                           # Public packages (if any)
│   ├── errors/                    # Error utilities
│   ├── retry/                     # Retry logic
│   └── validation/                # Input validation
│
├── scripts/                       # Build and utility scripts
│   ├── setup.sh                   # Development environment setup
│   ├── build.sh                   # Build script
│   └── release.sh                 # Release automation
│
├── deployments/                   # Deployment configurations
│   ├── docker/                    # Dockerfiles
│   ├── k8s/                       # Kubernetes manifests
│   │   ├── base/                  # Base configurations
│   │   └── overlays/              # Environment-specific overlays
│   └── kind/                      # Kind cluster setup
│
├── configs/                       # Configuration files
│   ├── development.yaml           # Development config
│   ├── production.yaml            # Production config
│   └── searxng/                   # SearXNG configuration
│
├── docs/                          # Documentation
│   ├── ARCHITECTURE.md            # Architecture overview
│   ├── DEVELOPMENT.md             # Development guide
│   ├── API.md                     # API documentation
│   ├── DEPLOYMENT.md              # Deployment guide
│   └── diagrams/                  # Architecture diagrams
│
├── test/                          # Test utilities and fixtures
│   ├── fixtures/                  # Test data
│   ├── integration/               # Integration tests
│   ├── e2e/                       # End-to-end tests
│   └── testutil/                  # Test utilities
│
├── .github/                       # GitHub specific files
│   ├── workflows/                 # GitHub Actions
│   ├── ISSUE_TEMPLATE/            # Issue templates
│   └── PULL_REQUEST_TEMPLATE.md   # PR template
│
├── Makefile                       # Build automation
├── Dockerfile                     # Container image
├── docker-compose.yml             # Local development setup
├── go.mod                         # Go module definition
├── go.sum                         # Go module checksums
├── .env.example                   # Environment variables example
├── .gitignore                     # Git ignore rules
├── .golangci.yml                  # Linter configuration
├── README.md                      # Project overview
├── LICENSE                        # License file
└── CONTRIBUTING.md                # Contribution guidelines
```

## Package Guidelines

### Internal Packages

#### `/internal/assistant`
Core business logic for the assistant, including request processing, context management, and response generation.

#### `/internal/ai`
AI provider abstractions and implementations. Each provider should implement the common interface defined in `provider.go`.

#### `/internal/tools`
Individual tool implementations. Each tool should:
- Implement the Tool interface
- Be self-contained
- Include its own tests
- Document its capabilities

#### `/internal/storage`
Data access layer with repository pattern. PostgreSQL-specific code should be isolated here.

#### `/internal/server`
HTTP server implementation using standard library. No external web frameworks.

#### `/internal/cli`
Command-line interface implementation with interactive mode support.

#### `/internal/web`
Web UI templates and components using Templ and HTMX.

### Public Packages (`/pkg`)

Only truly reusable packages that could be used by other projects should go here. Most code should be in `/internal`.

## File Naming Conventions

- Use lowercase with underscores for file names
- Test files: `*_test.go`
- SQL queries: `*.sql` (for sqlc)
- SQL migrations: `<version>_<description>.up.sql` and `.down.sql`
- Templates: `*.templ`
- Static assets: follow web conventions

## Development Principles

### Pure Go Standard Library Approach

This project strictly adheres to using Go's standard library for HTTP handling:

- **NO** web frameworks (Gin, Echo, Fiber, etc.)
- **NO** external routers (chi, mux, etc.)
- **YES** `net/http` with `ServeMux`
- **YES** Custom middleware chains
- **YES** `log/slog` for structured logging
- **YES** `fmt.Errorf` with `%w` for error wrapping

### Key Libraries and Tools

- **pgx** - PostgreSQL driver (not database/sql)
- **sqlc** - Type-safe SQL code generation
- **langchaingo** - LangChain implementation for Go
- **templ** - Type-safe HTML templates
- **testcontainers-go** - Integration testing with real dependencies

## Development Workflow

1. **SQL Changes**
   - Write migrations in `/internal/storage/postgres/migrations`
   - Update queries in `/internal/storage/postgres/queries`
   - Run `sqlc generate` to update models

2. **Adding New Tools**
   - Create new package under `/internal/tools/`
   - Implement Tool interface
   - Register in tool registry
   - Add tests

3. **UI Development**
   - Create Templ templates in `/internal/web/templates`
   - Use HTMX for interactivity
   - Follow Material Design 3 guidelines

## Configuration Management

- Use environment variables for secrets
- YAML files for structured configuration
- Support for multiple environments
- Configuration validation on startup

## Testing Structure

- Unit tests: Alongside implementation files
- Integration tests: `/test/integration`
- E2E tests: `/test/e2e`
- Fixtures: `/test/fixtures`
- Test utilities: `/test/testutil`

## Build and Deployment

- Makefile for common tasks
- Docker for containerization
- Kind for local Kubernetes
- GitHub Actions for CI/CD