version: "2"
sql:
  - engine: "postgresql"
    queries: "internal/storage/postgres/queries"
    schema: "internal/storage/postgres/schema"
    gen:
      go:
        package: "sqlc"
        out: "internal/storage/postgres/sqlc"
        sql_package: "pgx/v5"
        emit_json_tags: true
        emit_prepared_queries: true
        emit_interface: true
        emit_exact_table_names: false
        emit_empty_slices: true
        emit_exported_queries: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: false
        emit_methods_with_db_argument: false
        emit_pointers_for_null_types: false
        emit_enum_valid_method: true
        emit_all_enum_values: true
        overrides:
          - column: "*.created_at"
            go_type: "time.Time"
          - column: "*.updated_at"
            go_type: "time.Time"
          - column: "*.metadata"
            go_type: "json.RawMessage"
          - column: "*.embedding"
            go_type: "[]float64"
            go_struct_tag: 'json:"embedding" db:"embedding,type:vector"`'
