package langchain

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/anthropic"
	"github.com/tmc/langchaingo/llms/googleai"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/config"
)

// LangChainClient wraps LangChain-Go functionality with our AI interface
type LangChainClient struct {
	llm      llms.Model
	memory   schema.Memory
	config   config.LangChain
	logger   *slog.Logger
	provider string
}

// NewLangChainClient creates a new LangChain-Go client
func NewLangChainClient(provider string, aiConfig interface{}, langchainConfig config.LangChain, logger *slog.Logger) (*LangChainClient, error) {
	var llm llms.Model
	var err error

	switch provider {
	case "claude":
		claude<PERSON>onfig, ok := aiConfig.(config.Claude)
		if !ok {
			return nil, fmt.Errorf("invalid Claude configuration")
		}
		llm, err = anthropic.New(
			anthropic.WithToken(claudeConfig.APIKey),
			anthropic.WithModel(claudeConfig.Model),
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create Anthropic LLM: %w", err)
		}

	case "gemini":
		geminiConfig, ok := aiConfig.(config.Gemini)
		if !ok {
			return nil, fmt.Errorf("invalid Gemini configuration")
		}
		llm, err = googleai.New(
			context.Background(),
			googleai.WithAPIKey(geminiConfig.APIKey),
			googleai.WithDefaultModel(geminiConfig.Model),
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create Google AI LLM: %w", err)
		}

	default:
		return nil, fmt.Errorf("unsupported provider: %s", provider)
	}

	// Initialize memory if enabled
	var mem schema.Memory
	if langchainConfig.EnableMemory {
		mem = memory.NewConversationBuffer(langchainConfig.MemorySize)
	}

	return &LangChainClient{
		llm:      llm,
		memory:   mem,
		config:   langchainConfig,
		logger:   logger,
		provider: provider,
	}, nil
}

// Name returns the provider name
func (c *LangChainClient) Name() string {
	return fmt.Sprintf("langchain-%s", c.provider)
}

// GenerateResponse generates a response using LangChain-Go
func (c *LangChainClient) GenerateResponse(ctx context.Context, request *ai.GenerateRequest) (*ai.GenerateResponse, error) {
	startTime := time.Now()

	c.logger.Debug("Generating response with LangChain",
		slog.String("provider", c.provider),
		slog.String("model", request.Model),
		slog.Int("message_count", len(request.Messages)))

	// Convert messages to LangChain format
	messages := c.convertToLangChainMessages(request.Messages)

	// Add system prompt if provided
	if request.SystemPrompt != nil && *request.SystemPrompt != "" {
		systemMsg := schema.SystemChatMessage{
			Content: *request.SystemPrompt,
		}
		messages = append([]schema.ChatMessage{systemMsg}, messages...)
	}

	// Add messages to memory if enabled
	if c.memory != nil {
		for _, msg := range messages {
			c.memory.ChatHistory().AddMessage(ctx, msg)
		}
	}

	// Prepare generation options
	options := []llms.CallOption{
		llms.WithMaxTokens(request.MaxTokens),
		llms.WithTemperature(request.Temperature),
	}

	if request.Model != "" {
		options = append(options, llms.WithModel(request.Model))
	}

	// Generate response
	response, err := c.llm.GenerateContent(ctx, messages, options...)
	if err != nil {
		c.logger.Error("LangChain generation failed",
			slog.String("provider", c.provider),
			slog.Any("error", err))
		return nil, fmt.Errorf("LangChain generation failed: %w", err)
	}

	// Extract content from response
	content := ""
	if len(response.Choices) > 0 {
		content = response.Choices[0].Content
	}

	// Add assistant response to memory if enabled
	if c.memory != nil && content != "" {
		assistantMsg := schema.AIChatMessage{
			Content: content,
		}
		c.memory.ChatHistory().AddMessage(ctx, assistantMsg)
	}

	// Calculate token usage (approximate)
	tokenUsage := c.estimateTokenUsage(request.Messages, content)

	aiResponse := &ai.GenerateResponse{
		Content:      content,
		Model:        c.getModelFromResponse(response),
		Provider:     c.Name(),
		TokensUsed:   tokenUsage,
		FinishReason: c.getFinishReason(response),
		ResponseTime: time.Since(startTime),
		RequestID:    c.generateRequestID(),
		Metadata:     request.Metadata,
	}

	c.logger.Debug("LangChain response generated",
		slog.String("provider", c.provider),
		slog.Int("response_length", len(content)),
		slog.Duration("response_time", aiResponse.ResponseTime))

	return aiResponse, nil
}

// GenerateEmbedding generates embeddings (not directly supported by LangChain-Go core)
func (c *LangChainClient) GenerateEmbedding(ctx context.Context, text string) (*ai.EmbeddingResponse, error) {
	// LangChain-Go doesn't have direct embedding support in the core
	// This would need to be implemented using specific embedding models
	return nil, fmt.Errorf("embedding generation not implemented for LangChain provider")
}

// Health checks if the LangChain client is healthy
func (c *LangChainClient) Health(ctx context.Context) error {
	// Simple health check by generating a minimal response
	testMessages := []schema.ChatMessage{
		schema.HumanChatMessage{Content: "Hello"},
	}

	_, err := c.llm.GenerateContent(ctx, testMessages, llms.WithMaxTokens(1))
	if err != nil {
		return fmt.Errorf("LangChain health check failed: %w", err)
	}

	return nil
}

// Close closes the LangChain client
func (c *LangChainClient) Close(ctx context.Context) error {
	// LangChain-Go doesn't require explicit cleanup
	return nil
}

// GetUsage returns usage statistics
func (c *LangChainClient) GetUsage(ctx context.Context) (*ai.UsageStats, error) {
	// LangChain-Go doesn't provide built-in usage tracking
	// This would need to be implemented separately
	return &ai.UsageStats{
		TotalRequests:   0,
		TotalTokens:     0,
		InputTokens:     0,
		OutputTokens:    0,
		TotalCost:       0,
		AverageLatency:  0,
		ErrorRate:       0,
		LastRequestTime: nil,
		RequestsPerHour: 0,
	}, nil
}

// Helper methods

func (c *LangChainClient) convertToLangChainMessages(messages []ai.Message) []schema.ChatMessage {
	langchainMessages := make([]schema.ChatMessage, 0, len(messages))

	for _, msg := range messages {
		switch msg.Role {
		case "user", "human":
			langchainMessages = append(langchainMessages, schema.HumanChatMessage{
				Content: msg.Content,
			})
		case "assistant", "ai":
			langchainMessages = append(langchainMessages, schema.AIChatMessage{
				Content: msg.Content,
			})
		case "system":
			langchainMessages = append(langchainMessages, schema.SystemChatMessage{
				Content: msg.Content,
			})
		default:
			// Default to human message
			langchainMessages = append(langchainMessages, schema.HumanChatMessage{
				Content: msg.Content,
			})
		}
	}

	return langchainMessages
}

func (c *LangChainClient) estimateTokenUsage(messages []ai.Message, response string) ai.TokenUsage {
	// Simple token estimation (4 characters ≈ 1 token for English)
	inputChars := 0
	for _, msg := range messages {
		inputChars += len(msg.Content)
	}

	inputTokens := inputChars / 4
	outputTokens := len(response) / 4
	totalTokens := inputTokens + outputTokens

	return ai.TokenUsage{
		InputTokens:  inputTokens,
		OutputTokens: outputTokens,
		TotalTokens:  totalTokens,
	}
}

func (c *LangChainClient) getModelFromResponse(response *llms.ContentResponse) string {
	// LangChain-Go doesn't always provide model info in response
	// Return the configured model or a default
	return fmt.Sprintf("%s-model", c.provider)
}

func (c *LangChainClient) getFinishReason(response *llms.ContentResponse) string {
	if len(response.Choices) > 0 {
		// LangChain-Go doesn't provide finish reason in the same way
		// Return a default value
		return "stop"
	}
	return "unknown"
}

func (c *LangChainClient) generateRequestID() string {
	return fmt.Sprintf("langchain-%s-%d", c.provider, time.Now().UnixNano())
}

// GetMemory returns the conversation memory if enabled
func (c *LangChainClient) GetMemory() schema.Memory {
	return c.memory
}

// ClearMemory clears the conversation memory
func (c *LangChainClient) ClearMemory(ctx context.Context) error {
	if c.memory != nil {
		c.memory.Clear(ctx)
	}
	return nil
}
