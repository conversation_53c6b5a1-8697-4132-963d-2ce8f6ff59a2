package postgres

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strconv"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/koopa0/assistant-go/internal/storage/postgres/sqlc"
)

// SQLCClient wraps the sqlc-generated queries with our domain types
type SQLCClient struct {
	pool    *pgxpool.Pool
	queries *sqlc.Queries
	logger  *slog.Logger
}

// NewSQLCClient creates a new SQLC-based database client
func NewSQLCClient(pool *pgxpool.Pool, logger *slog.Logger) *SQLCClient {
	return &SQLCClient{
		pool:    pool,
		queries: sqlc.New(pool),
		logger:  logger,
	}
}

// Health checks the database connection
func (c *SQLCClient) Health(ctx context.Context) error {
	return c.pool.Ping(ctx)
}

// Close closes the database connection pool
func (c *SQLCClient) Close() error {
	c.pool.Close()
	return nil
}

// Conversation domain methods

// CreateConversation creates a new conversation
func (c *SQLCClient) CreateConversation(ctx context.Context, userID, title string, metadata map[string]interface{}) (*Conversation, error) {
	metadataJSON, err := json.Marshal(metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal metadata: %w", err)
	}

	params := sqlc.CreateConversationParams{
		UserID:   userID,
		Title:    title,
		Metadata: metadataJSON,
	}

	sqlcConv, err := c.queries.CreateConversation(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create conversation: %w", err)
	}

	return c.convertSQLCConversation(sqlcConv), nil
}

// GetConversation retrieves a conversation by ID
func (c *SQLCClient) GetConversation(ctx context.Context, id string) (*Conversation, error) {
	uuid, err := c.parseUUID(id)
	if err != nil {
		return nil, fmt.Errorf("invalid conversation ID: %w", err)
	}

	sqlcConv, err := c.queries.GetConversation(ctx, uuid)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("conversation not found")
		}
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}

	return c.convertSQLCConversation(sqlcConv), nil
}

// GetConversationsByUser retrieves conversations for a user with pagination
func (c *SQLCClient) GetConversationsByUser(ctx context.Context, userID string, limit, offset int) ([]*Conversation, error) {
	params := sqlc.GetConversationsByUserParams{
		UserID: userID,
		Limit:  int32(limit),
		Offset: int32(offset),
	}

	sqlcConvs, err := c.queries.GetConversationsByUser(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversations: %w", err)
	}

	conversations := make([]*Conversation, len(sqlcConvs))
	for i, sqlcConv := range sqlcConvs {
		conversations[i] = c.convertSQLCConversation(sqlcConv)
	}

	return conversations, nil
}

// Message domain methods

// CreateMessage creates a new message
func (c *SQLCClient) CreateMessage(ctx context.Context, conversationID, role, content string, tokenCount int, metadata map[string]interface{}) (*Message, error) {
	convUUID, err := c.parseUUID(conversationID)
	if err != nil {
		return nil, fmt.Errorf("invalid conversation ID: %w", err)
	}

	metadataJSON, err := json.Marshal(metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal metadata: %w", err)
	}

	params := sqlc.CreateMessageParams{
		ConversationID: convUUID,
		Role:           role,
		Content:        content,
		TokenCount:     pgtype.Int4{Int32: int32(tokenCount), Valid: true},
		Metadata:       metadataJSON,
	}

	sqlcMsg, err := c.queries.CreateMessage(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create message: %w", err)
	}

	return c.convertSQLCMessage(sqlcMsg), nil
}

// GetMessagesByConversation retrieves messages for a conversation
func (c *SQLCClient) GetMessagesByConversation(ctx context.Context, conversationID string, limit, offset int) ([]*Message, error) {
	convUUID, err := c.parseUUID(conversationID)
	if err != nil {
		return nil, fmt.Errorf("invalid conversation ID: %w", err)
	}

	var sqlcMsgs []*sqlc.Message
	if limit <= 0 {
		// Get all messages
		sqlcMsgs, err = c.queries.GetAllMessagesByConversation(ctx, convUUID)
	} else {
		// Get paginated messages
		params := sqlc.GetMessagesByConversationParams{
			ConversationID: convUUID,
			Limit:          int32(limit),
			Offset:         int32(offset),
		}
		sqlcMsgs, err = c.queries.GetMessagesByConversation(ctx, params)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get messages: %w", err)
	}

	messages := make([]*Message, len(sqlcMsgs))
	for i, sqlcMsg := range sqlcMsgs {
		messages[i] = c.convertSQLCMessage(sqlcMsg)
	}

	return messages, nil
}

// Embedding domain methods

// CreateEmbedding creates or updates an embedding
func (c *SQLCClient) CreateEmbedding(ctx context.Context, contentType, contentID, contentText string, embedding []float64, metadata map[string]interface{}) (*EmbeddingRecord, error) {
	metadataJSON, err := json.Marshal(metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal metadata: %w", err)
	}

	embeddingStr := c.vectorToString(embedding)

	params := sqlc.CreateEmbeddingParams{
		ContentType: contentType,
		ContentID:   contentID,
		ContentText: contentText,
		Embedding:   embeddingStr,
		Metadata:    metadataJSON,
	}

	sqlcEmb, err := c.queries.CreateEmbedding(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create embedding: %w", err)
	}

	return c.convertSQLCEmbedding(sqlcEmb), nil
}

// SearchSimilarEmbeddings searches for similar embeddings
// Note: This is a simplified implementation. For production, use proper vector similarity search.
func (c *SQLCClient) SearchSimilarEmbeddings(ctx context.Context, queryEmbedding []float64, contentType string, limit int, threshold float64) ([]*EmbeddingSearchResult, error) {
	// For now, use a simple query to get embeddings by content type
	// In production, this should use proper vector similarity search
	params := sqlc.GetEmbeddingsByTypeParams{
		ContentType: contentType,
		Limit:       int32(limit),
		Offset:      0,
	}

	sqlcEmbeddings, err := c.queries.GetEmbeddingsByType(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to search embeddings: %w", err)
	}

	results := make([]*EmbeddingSearchResult, len(sqlcEmbeddings))
	for i, sqlcEmb := range sqlcEmbeddings {
		// Mock similarity calculation for now
		similarity := 0.8 // This should be calculated using actual vector similarity
		results[i] = &EmbeddingSearchResult{
			Record:     c.convertSQLCEmbedding(sqlcEmb),
			Similarity: similarity,
			Distance:   1.0 - similarity,
		}
	}

	return results, nil
}

// Helper methods for type conversion

func (c *SQLCClient) parseUUID(id string) (pgtype.UUID, error) {
	var uuid pgtype.UUID
	err := uuid.Scan(id)
	return uuid, err
}

func (c *SQLCClient) uuidToString(uuid pgtype.UUID) string {
	if !uuid.Valid {
		return ""
	}
	// Convert UUID bytes to string format
	return fmt.Sprintf("%x-%x-%x-%x-%x",
		uuid.Bytes[0:4],
		uuid.Bytes[4:6],
		uuid.Bytes[6:8],
		uuid.Bytes[8:10],
		uuid.Bytes[10:16])
}

func (c *SQLCClient) vectorToString(vector []float64) string {
	if len(vector) == 0 {
		return "[]"
	}

	parts := make([]string, len(vector))
	for i, v := range vector {
		parts[i] = strconv.FormatFloat(v, 'f', 6, 64)
	}

	return "[" + strings.Join(parts, ",") + "]"
}

func (c *SQLCClient) stringToVector(vectorStr string) []float64 {
	if vectorStr == "" || vectorStr == "[]" {
		return make([]float64, 0)
	}

	// Remove brackets and split by comma
	vectorStr = strings.Trim(vectorStr, "[]")
	parts := strings.Split(vectorStr, ",")

	vector := make([]float64, 0, len(parts))
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		value, err := strconv.ParseFloat(part, 64)
		if err != nil {
			c.logger.Warn("Failed to parse vector component",
				slog.String("component", part),
				slog.String("vector_string", vectorStr),
				slog.Any("error", err))
			continue
		}

		vector = append(vector, value)
	}

	return vector
}

func (c *SQLCClient) convertSQLCConversation(sqlcConv *sqlc.Conversation) *Conversation {
	var metadata map[string]interface{}
	if len(sqlcConv.Metadata) > 0 {
		json.Unmarshal(sqlcConv.Metadata, &metadata)
	}
	if metadata == nil {
		metadata = make(map[string]interface{})
	}

	return &Conversation{
		ID:        c.uuidToString(sqlcConv.ID),
		UserID:    sqlcConv.UserID,
		Title:     sqlcConv.Title,
		Metadata:  metadata,
		CreatedAt: sqlcConv.CreatedAt,
		UpdatedAt: sqlcConv.UpdatedAt,
		Messages:  []*Message{}, // Will be loaded separately if needed
	}
}

func (c *SQLCClient) convertSQLCMessage(sqlcMsg *sqlc.Message) *Message {
	var metadata map[string]interface{}
	if len(sqlcMsg.Metadata) > 0 {
		json.Unmarshal(sqlcMsg.Metadata, &metadata)
	}
	if metadata == nil {
		metadata = make(map[string]interface{})
	}

	tokenCount := 0
	if sqlcMsg.TokenCount.Valid {
		tokenCount = int(sqlcMsg.TokenCount.Int32)
	}

	return &Message{
		ID:             c.uuidToString(sqlcMsg.ID),
		ConversationID: c.uuidToString(sqlcMsg.ConversationID),
		Role:           sqlcMsg.Role,
		Content:        sqlcMsg.Content,
		TokenCount:     tokenCount,
		Metadata:       metadata,
		CreatedAt:      sqlcMsg.CreatedAt,
	}
}

func (c *SQLCClient) convertSQLCEmbedding(sqlcEmb *sqlc.Embedding) *EmbeddingRecord {
	var metadata map[string]interface{}
	if len(sqlcEmb.Metadata) > 0 {
		json.Unmarshal(sqlcEmb.Metadata, &metadata)
	}
	if metadata == nil {
		metadata = make(map[string]interface{})
	}

	return &EmbeddingRecord{
		ID:          c.uuidToString(sqlcEmb.ID),
		ContentType: sqlcEmb.ContentType,
		ContentID:   c.uuidToString(sqlcEmb.ContentID),
		ContentText: sqlcEmb.ContentText,
		Embedding:   c.stringToVector(sqlcEmb.Embedding),
		Metadata:    metadata,
		CreatedAt:   sqlcEmb.CreatedAt,
	}
}

// Memory Entry domain methods

// CreateMemoryEntry creates a new memory entry
func (c *SQLCClient) CreateMemoryEntry(ctx context.Context, entry *MemoryEntryDomain) (*MemoryEntryDomain, error) {
	userUUID, err := c.parseUUID(entry.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	metadataJSON, err := json.Marshal(entry.Metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal metadata: %w", err)
	}

	var sessionID pgtype.Text
	if entry.SessionID != "" {
		sessionID = pgtype.Text{String: entry.SessionID, Valid: true}
	}

	var expiresAt pgtype.Timestamptz
	if entry.ExpiresAt != nil {
		expiresAt = pgtype.Timestamptz{Time: *entry.ExpiresAt, Valid: true}
	}

	params := sqlc.CreateMemoryEntryParams{
		MemoryType:  entry.Type,
		UserID:      userUUID,
		SessionID:   sessionID,
		Content:     entry.Content,
		Importance:  pgtype.Numeric{Int: pgtype.Int{Int64: int64(entry.Importance * 100), Valid: true}, Valid: true},
		AccessCount: pgtype.Int4{Int32: int32(entry.AccessCount), Valid: true},
		LastAccess:  pgtype.Timestamptz{Time: entry.LastAccess, Valid: true},
		ExpiresAt:   expiresAt,
		Metadata:    metadataJSON,
	}

	sqlcEntry, err := c.queries.CreateMemoryEntry(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create memory entry: %w", err)
	}

	return c.convertSQLCMemoryEntry(sqlcEntry), nil
}

// GetMemoryEntriesByUser retrieves memory entries for a user
func (c *SQLCClient) GetMemoryEntriesByUser(ctx context.Context, userID string, memoryTypes []string, limit, offset int) ([]*MemoryEntryDomain, error) {
	userUUID, err := c.parseUUID(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	params := sqlc.GetMemoryEntriesByUserParams{
		UserID: userUUID,
		Column2: memoryTypes,
		Limit:  int32(limit),
		Offset: int32(offset),
	}

	sqlcEntries, err := c.queries.GetMemoryEntriesByUser(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to get memory entries: %w", err)
	}

	entries := make([]*MemoryEntryDomain, len(sqlcEntries))
	for i, sqlcEntry := range sqlcEntries {
		entries[i] = c.convertSQLCMemoryEntry(sqlcEntry)
	}

	return entries, nil
}

// Agent Execution domain methods

// CreateAgentExecution creates a new agent execution record
func (c *SQLCClient) CreateAgentExecution(ctx context.Context, execution *AgentExecutionDomain) (*AgentExecutionDomain, error) {
	userUUID, err := c.parseUUID(execution.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	stepsJSON, err := json.Marshal(execution.Steps)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal steps: %w", err)
	}

	metadataJSON, err := json.Marshal(execution.Metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal metadata: %w", err)
	}

	params := sqlc.CreateAgentExecutionParams{
		AgentType:       execution.AgentType,
		UserID:          userUUID,
		Query:           execution.Query,
		Response:        pgtype.Text{String: execution.Response, Valid: execution.Response != ""},
		Steps:           stepsJSON,
		ExecutionTimeMs: pgtype.Int4{Int32: int32(execution.ExecutionTimeMs), Valid: true},
		Success:         pgtype.Bool{Bool: execution.Success, Valid: true},
		ErrorMessage:    pgtype.Text{String: execution.ErrorMessage, Valid: execution.ErrorMessage != ""},
		Metadata:        metadataJSON,
	}

	sqlcExecution, err := c.queries.CreateAgentExecution(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create agent execution: %w", err)
	}

	return c.convertSQLCAgentExecution(sqlcExecution), nil
}

// Chain Execution domain methods

// CreateChainExecution creates a new chain execution record
func (c *SQLCClient) CreateChainExecution(ctx context.Context, execution *ChainExecutionDomain) (*ChainExecutionDomain, error) {
	userUUID, err := c.parseUUID(execution.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	stepsJSON, err := json.Marshal(execution.Steps)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal steps: %w", err)
	}

	metadataJSON, err := json.Marshal(execution.Metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal metadata: %w", err)
	}

	params := sqlc.CreateChainExecutionParams{
		ChainType:       execution.ChainType,
		UserID:          userUUID,
		Input:           execution.Input,
		Output:          pgtype.Text{String: execution.Output, Valid: execution.Output != ""},
		Steps:           stepsJSON,
		ExecutionTimeMs: pgtype.Int4{Int32: int32(execution.ExecutionTimeMs), Valid: true},
		TokensUsed:      pgtype.Int4{Int32: int32(execution.TokensUsed), Valid: true},
		Success:         pgtype.Bool{Bool: execution.Success, Valid: true},
		ErrorMessage:    pgtype.Text{String: execution.ErrorMessage, Valid: execution.ErrorMessage != ""},
		Metadata:        metadataJSON,
	}

	sqlcExecution, err := c.queries.CreateChainExecution(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create chain execution: %w", err)
	}

	return c.convertSQLCChainExecution(sqlcExecution), nil
}
