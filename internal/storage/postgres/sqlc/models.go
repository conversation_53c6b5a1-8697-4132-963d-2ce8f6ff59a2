// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type AgentExecution struct {
	ID              pgtype.UUID     `json:"id"`
	AgentType       string          `json:"agent_type"`
	UserID          string          `json:"user_id"`
	Query           string          `json:"query"`
	Response        pgtype.Text     `json:"response"`
	Steps           []byte          `json:"steps"`
	ExecutionTimeMs pgtype.Int4     `json:"execution_time_ms"`
	Success         pgtype.Bool     `json:"success"`
	ErrorMessage    pgtype.Text     `json:"error_message"`
	CreatedAt       time.Time       `json:"created_at"`
	Metadata        json.RawMessage `json:"metadata"`
}

type ChainExecution struct {
	ID              pgtype.UUID     `json:"id"`
	ChainType       string          `json:"chain_type"`
	UserID          string          `json:"user_id"`
	Input           string          `json:"input"`
	Output          pgtype.Text     `json:"output"`
	Steps           []byte          `json:"steps"`
	ExecutionTimeMs pgtype.Int4     `json:"execution_time_ms"`
	TokensUsed      pgtype.Int4     `json:"tokens_used"`
	Success         pgtype.Bool     `json:"success"`
	ErrorMessage    pgtype.Text     `json:"error_message"`
	CreatedAt       time.Time       `json:"created_at"`
	Metadata        json.RawMessage `json:"metadata"`
}

// Stores conversation contexts and metadata
type Conversation struct {
	ID pgtype.UUID `json:"id"`
	// Identifier for the user who owns this conversation
	UserID string `json:"user_id"`
	// Human-readable title for the conversation
	Title string `json:"title"`
	// Additional metadata stored as JSON
	Metadata  json.RawMessage `json:"metadata"`
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
}

// Stores vector embeddings for semantic search
type Embedding struct {
	ID pgtype.UUID `json:"id"`
	// Type of content (e.g., message, document, code)
	ContentType string `json:"content_type"`
	// Unique identifier for the content within its type
	ContentID string `json:"content_id"`
	// The original text that was embedded
	ContentText string `json:"content_text"`
	// Vector embedding of the content
	Embedding string `json:"embedding"`
	// Additional embedding metadata stored as JSON
	Metadata  json.RawMessage `json:"metadata"`
	CreatedAt time.Time       `json:"created_at"`
}

type MemoryEntry struct {
	ID          pgtype.UUID        `json:"id"`
	MemoryType  string             `json:"memory_type"`
	UserID      string             `json:"user_id"`
	SessionID   pgtype.Text        `json:"session_id"`
	Content     string             `json:"content"`
	Importance  pgtype.Numeric     `json:"importance"`
	AccessCount pgtype.Int4        `json:"access_count"`
	LastAccess  pgtype.Timestamptz `json:"last_access"`
	CreatedAt   time.Time          `json:"created_at"`
	ExpiresAt   pgtype.Timestamptz `json:"expires_at"`
	Metadata    json.RawMessage    `json:"metadata"`
}

// Stores individual messages within conversations
type Message struct {
	ID pgtype.UUID `json:"id"`
	// Reference to the parent conversation
	ConversationID pgtype.UUID `json:"conversation_id"`
	// Role of the message sender (user, assistant, system)
	Role string `json:"role"`
	// The actual message content
	Content string `json:"content"`
	// Number of tokens in the message content
	TokenCount pgtype.Int4 `json:"token_count"`
	// Additional message metadata stored as JSON
	Metadata  json.RawMessage `json:"metadata"`
	CreatedAt time.Time       `json:"created_at"`
}

type ToolCache struct {
	ID              pgtype.UUID        `json:"id"`
	UserID          string             `json:"user_id"`
	ToolName        string             `json:"tool_name"`
	InputHash       string             `json:"input_hash"`
	InputData       []byte             `json:"input_data"`
	OutputData      []byte             `json:"output_data"`
	ExecutionTimeMs pgtype.Int4        `json:"execution_time_ms"`
	Success         pgtype.Bool        `json:"success"`
	ErrorMessage    pgtype.Text        `json:"error_message"`
	HitCount        pgtype.Int4        `json:"hit_count"`
	LastHit         pgtype.Timestamptz `json:"last_hit"`
	CreatedAt       time.Time          `json:"created_at"`
	ExpiresAt       pgtype.Timestamptz `json:"expires_at"`
	Metadata        json.RawMessage    `json:"metadata"`
}

type UserContext struct {
	ID           pgtype.UUID        `json:"id"`
	UserID       string             `json:"user_id"`
	ContextType  string             `json:"context_type"`
	ContextKey   string             `json:"context_key"`
	ContextValue []byte             `json:"context_value"`
	Importance   pgtype.Numeric     `json:"importance"`
	CreatedAt    time.Time          `json:"created_at"`
	UpdatedAt    time.Time          `json:"updated_at"`
	ExpiresAt    pgtype.Timestamptz `json:"expires_at"`
	Metadata     json.RawMessage    `json:"metadata"`
}

type UserPreference struct {
	ID              pgtype.UUID     `json:"id"`
	UserID          string          `json:"user_id"`
	Category        string          `json:"category"`
	PreferenceKey   string          `json:"preference_key"`
	PreferenceValue []byte          `json:"preference_value"`
	ValueType       string          `json:"value_type"`
	Description     pgtype.Text     `json:"description"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
	Metadata        json.RawMessage `json:"metadata"`
}
