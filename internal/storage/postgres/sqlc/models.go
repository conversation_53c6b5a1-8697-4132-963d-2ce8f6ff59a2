// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type AgentExecution struct {
	ID              pgtype.UUID     `json:"id"`
	AgentType       string          `json:"agent_type"`
	UserID          pgtype.UUID     `json:"user_id"`
	ConversationID  pgtype.UUID     `json:"conversation_id"`
	Query           string          `json:"query"`
	Response        pgtype.Text     `json:"response"`
	Steps           []byte          `json:"steps"`
	ExecutionTimeMs pgtype.Int4     `json:"execution_time_ms"`
	Success         pgtype.Bool     `json:"success"`
	ErrorMessage    pgtype.Text     `json:"error_message"`
	CreatedAt       time.Time       `json:"created_at"`
	Metadata        json.RawMessage `json:"metadata"`
}

type AgentSession struct {
	ID             pgtype.UUID `json:"id"`
	UserID         pgtype.UUID `json:"user_id"`
	ConversationID pgtype.UUID `json:"conversation_id"`
	AgentType      string      `json:"agent_type"`
	SessionData    []byte      `json:"session_data"`
	MemoryData     []byte      `json:"memory_data"`
	Status         string      `json:"status"`
	CreatedAt      time.Time   `json:"created_at"`
	UpdatedAt      time.Time   `json:"updated_at"`
}

type AiProviderUsage struct {
	ID            pgtype.UUID `json:"id"`
	UserID        pgtype.UUID `json:"user_id"`
	Provider      string      `json:"provider"`
	Model         string      `json:"model"`
	OperationType string      `json:"operation_type"`
	InputTokens   pgtype.Int4 `json:"input_tokens"`
	OutputTokens  pgtype.Int4 `json:"output_tokens"`
	CostCents     pgtype.Int4 `json:"cost_cents"`
	RequestID     pgtype.Text `json:"request_id"`
	CreatedAt     time.Time   `json:"created_at"`
}

type ChainExecution struct {
	ID              pgtype.UUID     `json:"id"`
	ChainType       string          `json:"chain_type"`
	UserID          pgtype.UUID     `json:"user_id"`
	ConversationID  pgtype.UUID     `json:"conversation_id"`
	Input           string          `json:"input"`
	Output          pgtype.Text     `json:"output"`
	Steps           []byte          `json:"steps"`
	ExecutionTimeMs pgtype.Int4     `json:"execution_time_ms"`
	TokensUsed      pgtype.Int4     `json:"tokens_used"`
	Success         pgtype.Bool     `json:"success"`
	ErrorMessage    pgtype.Text     `json:"error_message"`
	CreatedAt       time.Time       `json:"created_at"`
	Metadata        json.RawMessage `json:"metadata"`
}

type CloudflareAccount struct {
	ID        pgtype.UUID     `json:"id"`
	UserID    pgtype.UUID     `json:"user_id"`
	Name      string          `json:"name"`
	ApiToken  pgtype.Text     `json:"api_token"`
	ApiKey    pgtype.Text     `json:"api_key"`
	Email     pgtype.Text     `json:"email"`
	AccountID pgtype.Text     `json:"account_id"`
	ZoneID    pgtype.Text     `json:"zone_id"`
	IsDefault pgtype.Bool     `json:"is_default"`
	Metadata  json.RawMessage `json:"metadata"`
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
}

type Conversation struct {
	ID         pgtype.UUID     `json:"id"`
	UserID     pgtype.UUID     `json:"user_id"`
	Title      string          `json:"title"`
	Summary    pgtype.Text     `json:"summary"`
	Metadata   json.RawMessage `json:"metadata"`
	IsArchived pgtype.Bool     `json:"is_archived"`
	CreatedAt  time.Time       `json:"created_at"`
	UpdatedAt  time.Time       `json:"updated_at"`
}

type DatabaseConnection struct {
	ID               pgtype.UUID     `json:"id"`
	UserID           pgtype.UUID     `json:"user_id"`
	Name             string          `json:"name"`
	ConnectionString string          `json:"connection_string"`
	Description      pgtype.Text     `json:"description"`
	IsDefault        pgtype.Bool     `json:"is_default"`
	Metadata         json.RawMessage `json:"metadata"`
	CreatedAt        time.Time       `json:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at"`
}

type DockerConnection struct {
	ID         pgtype.UUID     `json:"id"`
	UserID     pgtype.UUID     `json:"user_id"`
	Name       string          `json:"name"`
	Host       string          `json:"host"`
	ApiVersion pgtype.Text     `json:"api_version"`
	TlsVerify  pgtype.Bool     `json:"tls_verify"`
	CertPath   pgtype.Text     `json:"cert_path"`
	IsDefault  pgtype.Bool     `json:"is_default"`
	Metadata   json.RawMessage `json:"metadata"`
	CreatedAt  time.Time       `json:"created_at"`
	UpdatedAt  time.Time       `json:"updated_at"`
}

type Embedding struct {
	ID          pgtype.UUID     `json:"id"`
	ContentType string          `json:"content_type"`
	ContentID   pgtype.UUID     `json:"content_id"`
	ContentText string          `json:"content_text"`
	Embedding   string          `json:"embedding"`
	Metadata    json.RawMessage `json:"metadata"`
	CreatedAt   time.Time       `json:"created_at"`
}

type KubernetesCluster struct {
	ID         pgtype.UUID     `json:"id"`
	UserID     pgtype.UUID     `json:"user_id"`
	Name       string          `json:"name"`
	ConfigPath pgtype.Text     `json:"config_path"`
	Context    pgtype.Text     `json:"context"`
	Namespace  pgtype.Text     `json:"namespace"`
	IsDefault  pgtype.Bool     `json:"is_default"`
	Metadata   json.RawMessage `json:"metadata"`
	CreatedAt  time.Time       `json:"created_at"`
	UpdatedAt  time.Time       `json:"updated_at"`
}

type MemoryEntry struct {
	ID          pgtype.UUID        `json:"id"`
	MemoryType  string             `json:"memory_type"`
	UserID      pgtype.UUID        `json:"user_id"`
	SessionID   pgtype.Text        `json:"session_id"`
	Content     string             `json:"content"`
	Importance  pgtype.Numeric     `json:"importance"`
	AccessCount pgtype.Int4        `json:"access_count"`
	LastAccess  pgtype.Timestamptz `json:"last_access"`
	CreatedAt   time.Time          `json:"created_at"`
	ExpiresAt   pgtype.Timestamptz `json:"expires_at"`
	Metadata    json.RawMessage    `json:"metadata"`
}

type Message struct {
	ID             pgtype.UUID     `json:"id"`
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           string          `json:"role"`
	Content        string          `json:"content"`
	Metadata       json.RawMessage `json:"metadata"`
	TokenCount     pgtype.Int4     `json:"token_count"`
	CreatedAt      time.Time       `json:"created_at"`
}

type SearchCache struct {
	ID        pgtype.UUID        `json:"id"`
	QueryHash string             `json:"query_hash"`
	QueryText string             `json:"query_text"`
	Results   []byte             `json:"results"`
	Source    string             `json:"source"`
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	CreatedAt time.Time          `json:"created_at"`
}

type ToolCache struct {
	ID              pgtype.UUID        `json:"id"`
	UserID          pgtype.UUID        `json:"user_id"`
	ToolName        string             `json:"tool_name"`
	InputHash       string             `json:"input_hash"`
	InputData       []byte             `json:"input_data"`
	OutputData      []byte             `json:"output_data"`
	ExecutionTimeMs pgtype.Int4        `json:"execution_time_ms"`
	Success         pgtype.Bool        `json:"success"`
	ErrorMessage    pgtype.Text        `json:"error_message"`
	HitCount        pgtype.Int4        `json:"hit_count"`
	LastHit         pgtype.Timestamptz `json:"last_hit"`
	CreatedAt       time.Time          `json:"created_at"`
	ExpiresAt       pgtype.Timestamptz `json:"expires_at"`
	Metadata        json.RawMessage    `json:"metadata"`
}

type ToolExecution struct {
	ID              pgtype.UUID        `json:"id"`
	MessageID       pgtype.UUID        `json:"message_id"`
	ToolName        string             `json:"tool_name"`
	InputData       []byte             `json:"input_data"`
	OutputData      []byte             `json:"output_data"`
	Status          string             `json:"status"`
	ErrorMessage    pgtype.Text        `json:"error_message"`
	ExecutionTimeMs pgtype.Int4        `json:"execution_time_ms"`
	StartedAt       pgtype.Timestamptz `json:"started_at"`
	CompletedAt     pgtype.Timestamptz `json:"completed_at"`
}

type User struct {
	ID           pgtype.UUID `json:"id"`
	Username     string      `json:"username"`
	Email        string      `json:"email"`
	PasswordHash string      `json:"password_hash"`
	FullName     pgtype.Text `json:"full_name"`
	AvatarUrl    pgtype.Text `json:"avatar_url"`
	Preferences  []byte      `json:"preferences"`
	IsActive     pgtype.Bool `json:"is_active"`
	CreatedAt    time.Time   `json:"created_at"`
	UpdatedAt    time.Time   `json:"updated_at"`
}

type UserContext struct {
	ID           pgtype.UUID        `json:"id"`
	UserID       pgtype.UUID        `json:"user_id"`
	ContextType  string             `json:"context_type"`
	ContextKey   string             `json:"context_key"`
	ContextValue []byte             `json:"context_value"`
	Importance   pgtype.Numeric     `json:"importance"`
	CreatedAt    time.Time          `json:"created_at"`
	UpdatedAt    time.Time          `json:"updated_at"`
	ExpiresAt    pgtype.Timestamptz `json:"expires_at"`
	Metadata     json.RawMessage    `json:"metadata"`
}

type UserPreference struct {
	ID              pgtype.UUID     `json:"id"`
	UserID          pgtype.UUID     `json:"user_id"`
	Category        string          `json:"category"`
	PreferenceKey   string          `json:"preference_key"`
	PreferenceValue []byte          `json:"preference_value"`
	ValueType       string          `json:"value_type"`
	Description     pgtype.Text     `json:"description"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
	Metadata        json.RawMessage `json:"metadata"`
}
