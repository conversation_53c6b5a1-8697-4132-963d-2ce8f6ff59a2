// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

// Stores conversation contexts and metadata
type Conversation struct {
	ID pgtype.UUID `json:"id"`
	// Identifier for the user who owns this conversation
	UserID string `json:"user_id"`
	// Human-readable title for the conversation
	Title string `json:"title"`
	// Additional metadata stored as JSON
	Metadata  json.RawMessage `json:"metadata"`
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
}

// Stores vector embeddings for semantic search
type Embedding struct {
	ID pgtype.UUID `json:"id"`
	// Type of content (e.g., message, document, code)
	ContentType string `json:"content_type"`
	// Unique identifier for the content within its type
	ContentID string `json:"content_id"`
	// The original text that was embedded
	ContentText string `json:"content_text"`
	// Vector embedding of the content
	Embedding string `json:"embedding"`
	// Additional embedding metadata stored as JSON
	Metadata  json.RawMessage `json:"metadata"`
	CreatedAt time.Time       `json:"created_at"`
}

// Stores individual messages within conversations
type Message struct {
	ID pgtype.UUID `json:"id"`
	// Reference to the parent conversation
	ConversationID pgtype.UUID `json:"conversation_id"`
	// Role of the message sender (user, assistant, system)
	Role string `json:"role"`
	// The actual message content
	Content string `json:"content"`
	// Number of tokens in the message content
	TokenCount pgtype.Int4 `json:"token_count"`
	// Additional message metadata stored as JSON
	Metadata  json.RawMessage `json:"metadata"`
	CreatedAt time.Time       `json:"created_at"`
}
