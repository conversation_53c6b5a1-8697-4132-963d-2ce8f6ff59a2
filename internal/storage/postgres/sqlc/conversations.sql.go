// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: conversations.sql

package sqlc

import (
	"context"
	"encoding/json"

	"github.com/jackc/pgx/v5/pgtype"
)

const CreateConversation = `-- name: CreateConversation :one
INSERT INTO conversations (user_id, title, metadata)
VALUES ($1, $2, $3)
RETURNING id, user_id, title, metadata, created_at, updated_at
`

type CreateConversationParams struct {
	UserID   string          `json:"user_id"`
	Title    string          `json:"title"`
	Metadata json.RawMessage `json:"metadata"`
}

func (q *Queries) CreateConversation(ctx context.Context, arg CreateConversationParams) (*Conversation, error) {
	row := q.db.QueryRow(ctx, CreateConversation, arg.UserID, arg.Title, arg.Metadata)
	var i Conversation
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Title,
		&i.Metadata,
		&i.<PERSON>At,
		&i.<PERSON>t,
	)
	return &i, err
}

const DeleteConversation = `-- name: DeleteConversation :exec
DELETE FROM conversations
WHERE id = $1
`

func (q *Queries) DeleteConversation(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteConversation, id)
	return err
}

const GetConversation = `-- name: GetConversation :one
SELECT id, user_id, title, metadata, created_at, updated_at
FROM conversations
WHERE id = $1
`

func (q *Queries) GetConversation(ctx context.Context, id pgtype.UUID) (*Conversation, error) {
	row := q.db.QueryRow(ctx, GetConversation, id)
	var i Conversation
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Title,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetConversationCount = `-- name: GetConversationCount :one
SELECT COUNT(*)
FROM conversations
WHERE user_id = $1
`

func (q *Queries) GetConversationCount(ctx context.Context, userID string) (int64, error) {
	row := q.db.QueryRow(ctx, GetConversationCount, userID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const GetConversationsByUser = `-- name: GetConversationsByUser :many
SELECT id, user_id, title, metadata, created_at, updated_at
FROM conversations
WHERE user_id = $1
ORDER BY updated_at DESC
LIMIT $2 OFFSET $3
`

type GetConversationsByUserParams struct {
	UserID string `json:"user_id"`
	Limit  int32  `json:"limit"`
	Offset int32  `json:"offset"`
}

func (q *Queries) GetConversationsByUser(ctx context.Context, arg GetConversationsByUserParams) ([]*Conversation, error) {
	rows, err := q.db.Query(ctx, GetConversationsByUser, arg.UserID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Conversation{}
	for rows.Next() {
		var i Conversation
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Title,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRecentConversations = `-- name: GetRecentConversations :many
SELECT id, user_id, title, metadata, created_at, updated_at
FROM conversations
WHERE user_id = $1
ORDER BY updated_at DESC
LIMIT $2
`

type GetRecentConversationsParams struct {
	UserID string `json:"user_id"`
	Limit  int32  `json:"limit"`
}

func (q *Queries) GetRecentConversations(ctx context.Context, arg GetRecentConversationsParams) ([]*Conversation, error) {
	rows, err := q.db.Query(ctx, GetRecentConversations, arg.UserID, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Conversation{}
	for rows.Next() {
		var i Conversation
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Title,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchConversations = `-- name: SearchConversations :many
SELECT id, user_id, title, metadata, created_at, updated_at
FROM conversations
WHERE user_id = $1 
  AND (title ILIKE '%' || $2 || '%' OR metadata::text ILIKE '%' || $2 || '%')
ORDER BY updated_at DESC
LIMIT $3 OFFSET $4
`

type SearchConversationsParams struct {
	UserID  string      `json:"user_id"`
	Column2 pgtype.Text `json:"column_2"`
	Limit   int32       `json:"limit"`
	Offset  int32       `json:"offset"`
}

func (q *Queries) SearchConversations(ctx context.Context, arg SearchConversationsParams) ([]*Conversation, error) {
	rows, err := q.db.Query(ctx, SearchConversations,
		arg.UserID,
		arg.Column2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Conversation{}
	for rows.Next() {
		var i Conversation
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Title,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateConversation = `-- name: UpdateConversation :one
UPDATE conversations
SET title = $2, metadata = $3, updated_at = NOW()
WHERE id = $1
RETURNING id, user_id, title, metadata, created_at, updated_at
`

type UpdateConversationParams struct {
	ID       pgtype.UUID     `json:"id"`
	Title    string          `json:"title"`
	Metadata json.RawMessage `json:"metadata"`
}

func (q *Queries) UpdateConversation(ctx context.Context, arg UpdateConversationParams) (*Conversation, error) {
	row := q.db.QueryRow(ctx, UpdateConversation, arg.ID, arg.Title, arg.Metadata)
	var i Conversation
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Title,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
