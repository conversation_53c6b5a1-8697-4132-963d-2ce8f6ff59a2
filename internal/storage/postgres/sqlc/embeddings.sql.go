// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: embeddings.sql

package sqlc

import (
	"context"
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/pgvector/pgvector-go"
)

const CreateEmbedding = `-- name: CreateEmbedding :one
INSERT INTO embeddings (content_type, content_id, content_text, embedding, metadata)
VALUES ($1, $2, $3, $4, $5)
ON CONFLICT (content_type, content_id) 
DO UPDATE SET 
    content_text = EXCLUDED.content_text,
    embedding = EXCLUDED.embedding,
    metadata = EXCLUDED.metadata,
    created_at = NOW()
RETURNING id, content_type, content_id, content_text, embedding, metadata, created_at
`

type CreateEmbeddingParams struct {
	ContentType string          `json:"content_type"`
	ContentID   pgtype.UUID     `json:"content_id"`
	ContentText string          `json:"content_text"`
	Embedding   string          `json:"embedding"`
	Metadata    json.RawMessage `json:"metadata"`
}

func (q *Queries) CreateEmbedding(ctx context.Context, arg CreateEmbeddingParams) (*Embedding, error) {
	row := q.db.QueryRow(ctx, CreateEmbedding,
		arg.ContentType,
		arg.ContentID,
		arg.ContentText,
		arg.Embedding,
		arg.Metadata,
	)
	var i Embedding
	err := row.Scan(
		&i.ID,
		&i.ContentType,
		&i.ContentID,
		&i.ContentText,
		&i.Embedding,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const DeleteEmbedding = `-- name: DeleteEmbedding :exec
DELETE FROM embeddings
WHERE content_type = $1 AND content_id = $2
`

type DeleteEmbeddingParams struct {
	ContentType string      `json:"content_type"`
	ContentID   pgtype.UUID `json:"content_id"`
}

func (q *Queries) DeleteEmbedding(ctx context.Context, arg DeleteEmbeddingParams) error {
	_, err := q.db.Exec(ctx, DeleteEmbedding, arg.ContentType, arg.ContentID)
	return err
}

const DeleteEmbeddingByID = `-- name: DeleteEmbeddingByID :exec
DELETE FROM embeddings
WHERE id = $1
`

func (q *Queries) DeleteEmbeddingByID(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteEmbeddingByID, id)
	return err
}

const GetAllEmbeddingCount = `-- name: GetAllEmbeddingCount :one
SELECT COUNT(*)
FROM embeddings
`

func (q *Queries) GetAllEmbeddingCount(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, GetAllEmbeddingCount)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const GetEmbedding = `-- name: GetEmbedding :one
SELECT id, content_type, content_id, content_text, embedding, metadata, created_at
FROM embeddings
WHERE content_type = $1 AND content_id = $2
`

type GetEmbeddingParams struct {
	ContentType string      `json:"content_type"`
	ContentID   pgtype.UUID `json:"content_id"`
}

func (q *Queries) GetEmbedding(ctx context.Context, arg GetEmbeddingParams) (*Embedding, error) {
	row := q.db.QueryRow(ctx, GetEmbedding, arg.ContentType, arg.ContentID)
	var i Embedding
	err := row.Scan(
		&i.ID,
		&i.ContentType,
		&i.ContentID,
		&i.ContentText,
		&i.Embedding,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const GetEmbeddingByID = `-- name: GetEmbeddingByID :one
SELECT id, content_type, content_id, content_text, embedding, metadata, created_at
FROM embeddings
WHERE id = $1
`

func (q *Queries) GetEmbeddingByID(ctx context.Context, id pgtype.UUID) (*Embedding, error) {
	row := q.db.QueryRow(ctx, GetEmbeddingByID, id)
	var i Embedding
	err := row.Scan(
		&i.ID,
		&i.ContentType,
		&i.ContentID,
		&i.ContentText,
		&i.Embedding,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const GetEmbeddingCount = `-- name: GetEmbeddingCount :one
SELECT COUNT(*)
FROM embeddings
WHERE content_type = $1
`

func (q *Queries) GetEmbeddingCount(ctx context.Context, contentType string) (int64, error) {
	row := q.db.QueryRow(ctx, GetEmbeddingCount, contentType)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const GetEmbeddingsByType = `-- name: GetEmbeddingsByType :many
SELECT id, content_type, content_id, content_text, embedding, metadata, created_at
FROM embeddings
WHERE content_type = $1
ORDER BY created_at DESC
LIMIT $2 OFFSET $3
`

type GetEmbeddingsByTypeParams struct {
	ContentType string `json:"content_type"`
	Limit       int32  `json:"limit"`
	Offset      int32  `json:"offset"`
}

func (q *Queries) GetEmbeddingsByType(ctx context.Context, arg GetEmbeddingsByTypeParams) ([]*Embedding, error) {
	rows, err := q.db.Query(ctx, GetEmbeddingsByType, arg.ContentType, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Embedding{}
	for rows.Next() {
		var i Embedding
		if err := rows.Scan(
			&i.ID,
			&i.ContentType,
			&i.ContentID,
			&i.ContentText,
			&i.Embedding,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchSimilarEmbeddings = `-- name: SearchSimilarEmbeddings :many
SELECT 
    id, 
    content_type, 
    content_id, 
    content_text, 
    embedding, 
    metadata, 
    created_at,
    1 - (embedding <=> $1::vector) AS similarity
FROM embeddings
WHERE content_type = $2
  AND 1 - (embedding <=> $1::vector) > $3
ORDER BY embedding <=> $1::vector
LIMIT $4
`

type SearchSimilarEmbeddingsParams struct {
	Column1     pgvector.Vector `json:"column_1"`
	ContentType string          `json:"content_type"`
	Embedding   string          `json:"embedding"`
	Limit       int32           `json:"limit"`
}

type SearchSimilarEmbeddingsRow struct {
	ID          pgtype.UUID     `json:"id"`
	ContentType string          `json:"content_type"`
	ContentID   pgtype.UUID     `json:"content_id"`
	ContentText string          `json:"content_text"`
	Embedding   string          `json:"embedding"`
	Metadata    json.RawMessage `json:"metadata"`
	CreatedAt   time.Time       `json:"created_at"`
	Similarity  int32           `json:"similarity"`
}

func (q *Queries) SearchSimilarEmbeddings(ctx context.Context, arg SearchSimilarEmbeddingsParams) ([]*SearchSimilarEmbeddingsRow, error) {
	rows, err := q.db.Query(ctx, SearchSimilarEmbeddings,
		arg.Column1,
		arg.ContentType,
		arg.Embedding,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SearchSimilarEmbeddingsRow{}
	for rows.Next() {
		var i SearchSimilarEmbeddingsRow
		if err := rows.Scan(
			&i.ID,
			&i.ContentType,
			&i.ContentID,
			&i.ContentText,
			&i.Embedding,
			&i.Metadata,
			&i.CreatedAt,
			&i.Similarity,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchSimilarEmbeddingsAllTypes = `-- name: SearchSimilarEmbeddingsAllTypes :many
SELECT 
    id, 
    content_type, 
    content_id, 
    content_text, 
    embedding, 
    metadata, 
    created_at,
    1 - (embedding <=> $1::vector) AS similarity
FROM embeddings
WHERE 1 - (embedding <=> $1::vector) > $2
ORDER BY embedding <=> $1::vector
LIMIT $3
`

type SearchSimilarEmbeddingsAllTypesParams struct {
	Column1   pgvector.Vector `json:"column_1"`
	Embedding string          `json:"embedding"`
	Limit     int32           `json:"limit"`
}

type SearchSimilarEmbeddingsAllTypesRow struct {
	ID          pgtype.UUID     `json:"id"`
	ContentType string          `json:"content_type"`
	ContentID   pgtype.UUID     `json:"content_id"`
	ContentText string          `json:"content_text"`
	Embedding   string          `json:"embedding"`
	Metadata    json.RawMessage `json:"metadata"`
	CreatedAt   time.Time       `json:"created_at"`
	Similarity  int32           `json:"similarity"`
}

func (q *Queries) SearchSimilarEmbeddingsAllTypes(ctx context.Context, arg SearchSimilarEmbeddingsAllTypesParams) ([]*SearchSimilarEmbeddingsAllTypesRow, error) {
	rows, err := q.db.Query(ctx, SearchSimilarEmbeddingsAllTypes, arg.Column1, arg.Embedding, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SearchSimilarEmbeddingsAllTypesRow{}
	for rows.Next() {
		var i SearchSimilarEmbeddingsAllTypesRow
		if err := rows.Scan(
			&i.ID,
			&i.ContentType,
			&i.ContentID,
			&i.ContentText,
			&i.Embedding,
			&i.Metadata,
			&i.CreatedAt,
			&i.Similarity,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateEmbedding = `-- name: UpdateEmbedding :one
UPDATE embeddings
SET content_text = $3, embedding = $4, metadata = $5
WHERE content_type = $1 AND content_id = $2
RETURNING id, content_type, content_id, content_text, embedding, metadata, created_at
`

type UpdateEmbeddingParams struct {
	ContentType string          `json:"content_type"`
	ContentID   pgtype.UUID     `json:"content_id"`
	ContentText string          `json:"content_text"`
	Embedding   string          `json:"embedding"`
	Metadata    json.RawMessage `json:"metadata"`
}

func (q *Queries) UpdateEmbedding(ctx context.Context, arg UpdateEmbeddingParams) (*Embedding, error) {
	row := q.db.QueryRow(ctx, UpdateEmbedding,
		arg.ContentType,
		arg.ContentID,
		arg.ContentText,
		arg.Embedding,
		arg.Metadata,
	)
	var i Embedding
	err := row.Scan(
		&i.ID,
		&i.ContentType,
		&i.ContentID,
		&i.ContentText,
		&i.Embedding,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}
