// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: messages.sql

package sqlc

import (
	"context"
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const CreateMessage = `-- name: CreateMessage :one
INSERT INTO messages (conversation_id, role, content, token_count, metadata)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, conversation_id, role, content, token_count, metadata, created_at
`

type CreateMessageParams struct {
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           string          `json:"role"`
	Content        string          `json:"content"`
	TokenCount     pgtype.Int4     `json:"token_count"`
	Metadata       json.RawMessage `json:"metadata"`
}

type CreateMessageRow struct {
	ID             pgtype.UUID     `json:"id"`
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           string          `json:"role"`
	Content        string          `json:"content"`
	TokenCount     pgtype.Int4     `json:"token_count"`
	Metadata       json.RawMessage `json:"metadata"`
	CreatedAt      time.Time       `json:"created_at"`
}

func (q *Queries) CreateMessage(ctx context.Context, arg CreateMessageParams) (*CreateMessageRow, error) {
	row := q.db.QueryRow(ctx, CreateMessage,
		arg.ConversationID,
		arg.Role,
		arg.Content,
		arg.TokenCount,
		arg.Metadata,
	)
	var i CreateMessageRow
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.Role,
		&i.Content,
		&i.TokenCount,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const DeleteMessage = `-- name: DeleteMessage :exec
DELETE FROM messages
WHERE id = $1
`

func (q *Queries) DeleteMessage(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteMessage, id)
	return err
}

const DeleteMessagesByConversation = `-- name: DeleteMessagesByConversation :exec
DELETE FROM messages
WHERE conversation_id = $1
`

func (q *Queries) DeleteMessagesByConversation(ctx context.Context, conversationID pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteMessagesByConversation, conversationID)
	return err
}

const GetAllMessagesByConversation = `-- name: GetAllMessagesByConversation :many
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE conversation_id = $1
ORDER BY created_at ASC
`

type GetAllMessagesByConversationRow struct {
	ID             pgtype.UUID     `json:"id"`
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           string          `json:"role"`
	Content        string          `json:"content"`
	TokenCount     pgtype.Int4     `json:"token_count"`
	Metadata       json.RawMessage `json:"metadata"`
	CreatedAt      time.Time       `json:"created_at"`
}

func (q *Queries) GetAllMessagesByConversation(ctx context.Context, conversationID pgtype.UUID) ([]*GetAllMessagesByConversationRow, error) {
	rows, err := q.db.Query(ctx, GetAllMessagesByConversation, conversationID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetAllMessagesByConversationRow{}
	for rows.Next() {
		var i GetAllMessagesByConversationRow
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokenCount,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetMessage = `-- name: GetMessage :one
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE id = $1
`

type GetMessageRow struct {
	ID             pgtype.UUID     `json:"id"`
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           string          `json:"role"`
	Content        string          `json:"content"`
	TokenCount     pgtype.Int4     `json:"token_count"`
	Metadata       json.RawMessage `json:"metadata"`
	CreatedAt      time.Time       `json:"created_at"`
}

func (q *Queries) GetMessage(ctx context.Context, id pgtype.UUID) (*GetMessageRow, error) {
	row := q.db.QueryRow(ctx, GetMessage, id)
	var i GetMessageRow
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.Role,
		&i.Content,
		&i.TokenCount,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const GetMessageCount = `-- name: GetMessageCount :one
SELECT COUNT(*)
FROM messages
WHERE conversation_id = $1
`

func (q *Queries) GetMessageCount(ctx context.Context, conversationID pgtype.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, GetMessageCount, conversationID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const GetMessagesByConversation = `-- name: GetMessagesByConversation :many
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE conversation_id = $1
ORDER BY created_at ASC
LIMIT $2 OFFSET $3
`

type GetMessagesByConversationParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	Limit          int32       `json:"limit"`
	Offset         int32       `json:"offset"`
}

type GetMessagesByConversationRow struct {
	ID             pgtype.UUID     `json:"id"`
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           string          `json:"role"`
	Content        string          `json:"content"`
	TokenCount     pgtype.Int4     `json:"token_count"`
	Metadata       json.RawMessage `json:"metadata"`
	CreatedAt      time.Time       `json:"created_at"`
}

func (q *Queries) GetMessagesByConversation(ctx context.Context, arg GetMessagesByConversationParams) ([]*GetMessagesByConversationRow, error) {
	rows, err := q.db.Query(ctx, GetMessagesByConversation, arg.ConversationID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetMessagesByConversationRow{}
	for rows.Next() {
		var i GetMessagesByConversationRow
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokenCount,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetMessagesByRole = `-- name: GetMessagesByRole :many
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE conversation_id = $1 AND role = $2
ORDER BY created_at ASC
`

type GetMessagesByRoleParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	Role           string      `json:"role"`
}

type GetMessagesByRoleRow struct {
	ID             pgtype.UUID     `json:"id"`
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           string          `json:"role"`
	Content        string          `json:"content"`
	TokenCount     pgtype.Int4     `json:"token_count"`
	Metadata       json.RawMessage `json:"metadata"`
	CreatedAt      time.Time       `json:"created_at"`
}

func (q *Queries) GetMessagesByRole(ctx context.Context, arg GetMessagesByRoleParams) ([]*GetMessagesByRoleRow, error) {
	rows, err := q.db.Query(ctx, GetMessagesByRole, arg.ConversationID, arg.Role)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetMessagesByRoleRow{}
	for rows.Next() {
		var i GetMessagesByRoleRow
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokenCount,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRecentMessages = `-- name: GetRecentMessages :many
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE conversation_id = $1
ORDER BY created_at DESC
LIMIT $2
`

type GetRecentMessagesParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	Limit          int32       `json:"limit"`
}

type GetRecentMessagesRow struct {
	ID             pgtype.UUID     `json:"id"`
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           string          `json:"role"`
	Content        string          `json:"content"`
	TokenCount     pgtype.Int4     `json:"token_count"`
	Metadata       json.RawMessage `json:"metadata"`
	CreatedAt      time.Time       `json:"created_at"`
}

func (q *Queries) GetRecentMessages(ctx context.Context, arg GetRecentMessagesParams) ([]*GetRecentMessagesRow, error) {
	rows, err := q.db.Query(ctx, GetRecentMessages, arg.ConversationID, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetRecentMessagesRow{}
	for rows.Next() {
		var i GetRecentMessagesRow
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokenCount,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchMessages = `-- name: SearchMessages :many
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE conversation_id = $1 
  AND content ILIKE '%' || $2 || '%'
ORDER BY created_at DESC
LIMIT $3 OFFSET $4
`

type SearchMessagesParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	Column2        pgtype.Text `json:"column_2"`
	Limit          int32       `json:"limit"`
	Offset         int32       `json:"offset"`
}

type SearchMessagesRow struct {
	ID             pgtype.UUID     `json:"id"`
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           string          `json:"role"`
	Content        string          `json:"content"`
	TokenCount     pgtype.Int4     `json:"token_count"`
	Metadata       json.RawMessage `json:"metadata"`
	CreatedAt      time.Time       `json:"created_at"`
}

func (q *Queries) SearchMessages(ctx context.Context, arg SearchMessagesParams) ([]*SearchMessagesRow, error) {
	rows, err := q.db.Query(ctx, SearchMessages,
		arg.ConversationID,
		arg.Column2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SearchMessagesRow{}
	for rows.Next() {
		var i SearchMessagesRow
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokenCount,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateMessage = `-- name: UpdateMessage :one
UPDATE messages
SET content = $2, token_count = $3, metadata = $4
WHERE id = $1
RETURNING id, conversation_id, role, content, token_count, metadata, created_at
`

type UpdateMessageParams struct {
	ID         pgtype.UUID     `json:"id"`
	Content    string          `json:"content"`
	TokenCount pgtype.Int4     `json:"token_count"`
	Metadata   json.RawMessage `json:"metadata"`
}

type UpdateMessageRow struct {
	ID             pgtype.UUID     `json:"id"`
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           string          `json:"role"`
	Content        string          `json:"content"`
	TokenCount     pgtype.Int4     `json:"token_count"`
	Metadata       json.RawMessage `json:"metadata"`
	CreatedAt      time.Time       `json:"created_at"`
}

func (q *Queries) UpdateMessage(ctx context.Context, arg UpdateMessageParams) (*UpdateMessageRow, error) {
	row := q.db.QueryRow(ctx, UpdateMessage,
		arg.ID,
		arg.Content,
		arg.TokenCount,
		arg.Metadata,
	)
	var i UpdateMessageRow
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.Role,
		&i.Content,
		&i.TokenCount,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}
