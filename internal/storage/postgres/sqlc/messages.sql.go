// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: messages.sql

package sqlc

import (
	"context"
	"encoding/json"

	"github.com/jackc/pgx/v5/pgtype"
)

const CreateMessage = `-- name: CreateMessage :one
INSERT INTO messages (conversation_id, role, content, token_count, metadata)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, conversation_id, role, content, token_count, metadata, created_at
`

type CreateMessageParams struct {
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           string          `json:"role"`
	Content        string          `json:"content"`
	TokenCount     pgtype.Int4     `json:"token_count"`
	Metadata       json.RawMessage `json:"metadata"`
}

func (q *Queries) CreateMessage(ctx context.Context, arg CreateMessageParams) (*Message, error) {
	row := q.db.QueryRow(ctx, CreateMessage,
		arg.ConversationID,
		arg.Role,
		arg.Content,
		arg.TokenCount,
		arg.Metadata,
	)
	var i Message
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.Role,
		&i.Content,
		&i.TokenCount,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const DeleteMessage = `-- name: DeleteMessage :exec
DELETE FROM messages
WHERE id = $1
`

func (q *Queries) DeleteMessage(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteMessage, id)
	return err
}

const DeleteMessagesByConversation = `-- name: DeleteMessagesByConversation :exec
DELETE FROM messages
WHERE conversation_id = $1
`

func (q *Queries) DeleteMessagesByConversation(ctx context.Context, conversationID pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteMessagesByConversation, conversationID)
	return err
}

const GetAllMessagesByConversation = `-- name: GetAllMessagesByConversation :many
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE conversation_id = $1
ORDER BY created_at ASC
`

func (q *Queries) GetAllMessagesByConversation(ctx context.Context, conversationID pgtype.UUID) ([]*Message, error) {
	rows, err := q.db.Query(ctx, GetAllMessagesByConversation, conversationID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Message{}
	for rows.Next() {
		var i Message
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokenCount,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetMessage = `-- name: GetMessage :one
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE id = $1
`

func (q *Queries) GetMessage(ctx context.Context, id pgtype.UUID) (*Message, error) {
	row := q.db.QueryRow(ctx, GetMessage, id)
	var i Message
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.Role,
		&i.Content,
		&i.TokenCount,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const GetMessageCount = `-- name: GetMessageCount :one
SELECT COUNT(*)
FROM messages
WHERE conversation_id = $1
`

func (q *Queries) GetMessageCount(ctx context.Context, conversationID pgtype.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, GetMessageCount, conversationID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const GetMessagesByConversation = `-- name: GetMessagesByConversation :many
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE conversation_id = $1
ORDER BY created_at ASC
LIMIT $2 OFFSET $3
`

type GetMessagesByConversationParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	Limit          int32       `json:"limit"`
	Offset         int32       `json:"offset"`
}

func (q *Queries) GetMessagesByConversation(ctx context.Context, arg GetMessagesByConversationParams) ([]*Message, error) {
	rows, err := q.db.Query(ctx, GetMessagesByConversation, arg.ConversationID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Message{}
	for rows.Next() {
		var i Message
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokenCount,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetMessagesByRole = `-- name: GetMessagesByRole :many
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE conversation_id = $1 AND role = $2
ORDER BY created_at ASC
`

type GetMessagesByRoleParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	Role           string      `json:"role"`
}

func (q *Queries) GetMessagesByRole(ctx context.Context, arg GetMessagesByRoleParams) ([]*Message, error) {
	rows, err := q.db.Query(ctx, GetMessagesByRole, arg.ConversationID, arg.Role)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Message{}
	for rows.Next() {
		var i Message
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokenCount,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRecentMessages = `-- name: GetRecentMessages :many
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE conversation_id = $1
ORDER BY created_at DESC
LIMIT $2
`

type GetRecentMessagesParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	Limit          int32       `json:"limit"`
}

func (q *Queries) GetRecentMessages(ctx context.Context, arg GetRecentMessagesParams) ([]*Message, error) {
	rows, err := q.db.Query(ctx, GetRecentMessages, arg.ConversationID, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Message{}
	for rows.Next() {
		var i Message
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokenCount,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchMessages = `-- name: SearchMessages :many
SELECT id, conversation_id, role, content, token_count, metadata, created_at
FROM messages
WHERE conversation_id = $1 
  AND content ILIKE '%' || $2 || '%'
ORDER BY created_at DESC
LIMIT $3 OFFSET $4
`

type SearchMessagesParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	Column2        pgtype.Text `json:"column_2"`
	Limit          int32       `json:"limit"`
	Offset         int32       `json:"offset"`
}

func (q *Queries) SearchMessages(ctx context.Context, arg SearchMessagesParams) ([]*Message, error) {
	rows, err := q.db.Query(ctx, SearchMessages,
		arg.ConversationID,
		arg.Column2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Message{}
	for rows.Next() {
		var i Message
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokenCount,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateMessage = `-- name: UpdateMessage :one
UPDATE messages
SET content = $2, token_count = $3, metadata = $4
WHERE id = $1
RETURNING id, conversation_id, role, content, token_count, metadata, created_at
`

type UpdateMessageParams struct {
	ID         pgtype.UUID     `json:"id"`
	Content    string          `json:"content"`
	TokenCount pgtype.Int4     `json:"token_count"`
	Metadata   json.RawMessage `json:"metadata"`
}

func (q *Queries) UpdateMessage(ctx context.Context, arg UpdateMessageParams) (*Message, error) {
	row := q.db.QueryRow(ctx, UpdateMessage,
		arg.ID,
		arg.Content,
		arg.TokenCount,
		arg.Metadata,
	)
	var i Message
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.Role,
		&i.Content,
		&i.TokenCount,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}
