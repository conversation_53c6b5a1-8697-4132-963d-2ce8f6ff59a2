// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: tools_and_preferences.sql

package sqlc

import (
	"context"
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const CreateToolCacheEntry = `-- name: CreateToolCacheEntry :one

INSERT INTO tool_cache (
    user_id,
    tool_name,
    input_hash,
    input_data,
    output_data,
    execution_time_ms,
    success,
    error_message,
    expires_at,
    metadata
) VALUES (
    $1::uuid, $2, $3, $4, $5, $6, $7, $8, $9, $10
) ON CONFLICT (user_id, tool_name, input_hash)
DO UPDATE SET
    output_data = EXCLUDED.output_data,
    execution_time_ms = EXCLUDED.execution_time_ms,
    success = EXCLUDED.success,
    error_message = EXCLUDED.error_message,
    hit_count = tool_cache.hit_count + 1,
    last_hit = NOW(),
    expires_at = EXCLUDED.expires_at,
    metadata = EXCLUDED.metadata
RETURNING id, user_id, tool_name, input_hash, input_data, output_data, execution_time_ms, success, error_message, hit_count, last_hit, created_at, expires_at, metadata
`

type CreateToolCacheEntryParams struct {
	Column1         pgtype.UUID        `json:"column_1"`
	ToolName        string             `json:"tool_name"`
	InputHash       string             `json:"input_hash"`
	InputData       []byte             `json:"input_data"`
	OutputData      []byte             `json:"output_data"`
	ExecutionTimeMs pgtype.Int4        `json:"execution_time_ms"`
	Success         pgtype.Bool        `json:"success"`
	ErrorMessage    pgtype.Text        `json:"error_message"`
	ExpiresAt       pgtype.Timestamptz `json:"expires_at"`
	Metadata        json.RawMessage    `json:"metadata"`
}

// Tool cache queries
func (q *Queries) CreateToolCacheEntry(ctx context.Context, arg CreateToolCacheEntryParams) (*ToolCache, error) {
	row := q.db.QueryRow(ctx, CreateToolCacheEntry,
		arg.Column1,
		arg.ToolName,
		arg.InputHash,
		arg.InputData,
		arg.OutputData,
		arg.ExecutionTimeMs,
		arg.Success,
		arg.ErrorMessage,
		arg.ExpiresAt,
		arg.Metadata,
	)
	var i ToolCache
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.ToolName,
		&i.InputHash,
		&i.InputData,
		&i.OutputData,
		&i.ExecutionTimeMs,
		&i.Success,
		&i.ErrorMessage,
		&i.HitCount,
		&i.LastHit,
		&i.CreatedAt,
		&i.ExpiresAt,
		&i.Metadata,
	)
	return &i, err
}

const CreateUserContext = `-- name: CreateUserContext :one

INSERT INTO user_context (
    user_id,
    context_type,
    context_key,
    context_value,
    importance,
    expires_at,
    metadata
) VALUES (
    $1::uuid, $2, $3, $4, $5, $6, $7
) ON CONFLICT (user_id, context_type, context_key)
DO UPDATE SET
    context_value = EXCLUDED.context_value,
    importance = EXCLUDED.importance,
    expires_at = EXCLUDED.expires_at,
    metadata = EXCLUDED.metadata,
    updated_at = NOW()
RETURNING id, user_id, context_type, context_key, context_value, importance, created_at, updated_at, expires_at, metadata
`

type CreateUserContextParams struct {
	Column1      pgtype.UUID        `json:"column_1"`
	ContextType  string             `json:"context_type"`
	ContextKey   string             `json:"context_key"`
	ContextValue []byte             `json:"context_value"`
	Importance   pgtype.Numeric     `json:"importance"`
	ExpiresAt    pgtype.Timestamptz `json:"expires_at"`
	Metadata     json.RawMessage    `json:"metadata"`
}

// User context queries
func (q *Queries) CreateUserContext(ctx context.Context, arg CreateUserContextParams) (*UserContext, error) {
	row := q.db.QueryRow(ctx, CreateUserContext,
		arg.Column1,
		arg.ContextType,
		arg.ContextKey,
		arg.ContextValue,
		arg.Importance,
		arg.ExpiresAt,
		arg.Metadata,
	)
	var i UserContext
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.ContextType,
		&i.ContextKey,
		&i.ContextValue,
		&i.Importance,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ExpiresAt,
		&i.Metadata,
	)
	return &i, err
}

const CreateUserPreference = `-- name: CreateUserPreference :one

INSERT INTO user_preferences (
    user_id,
    category,
    preference_key,
    preference_value,
    value_type,
    description,
    metadata
) VALUES (
    $1::uuid, $2, $3, $4, $5, $6, $7
) ON CONFLICT (user_id, category, preference_key)
DO UPDATE SET
    preference_value = EXCLUDED.preference_value,
    value_type = EXCLUDED.value_type,
    description = EXCLUDED.description,
    metadata = EXCLUDED.metadata,
    updated_at = NOW()
RETURNING id, user_id, category, preference_key, preference_value, value_type, description, created_at, updated_at, metadata
`

type CreateUserPreferenceParams struct {
	Column1         pgtype.UUID     `json:"column_1"`
	Category        string          `json:"category"`
	PreferenceKey   string          `json:"preference_key"`
	PreferenceValue []byte          `json:"preference_value"`
	ValueType       string          `json:"value_type"`
	Description     pgtype.Text     `json:"description"`
	Metadata        json.RawMessage `json:"metadata"`
}

// User preferences queries
func (q *Queries) CreateUserPreference(ctx context.Context, arg CreateUserPreferenceParams) (*UserPreference, error) {
	row := q.db.QueryRow(ctx, CreateUserPreference,
		arg.Column1,
		arg.Category,
		arg.PreferenceKey,
		arg.PreferenceValue,
		arg.ValueType,
		arg.Description,
		arg.Metadata,
	)
	var i UserPreference
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Category,
		&i.PreferenceKey,
		&i.PreferenceValue,
		&i.ValueType,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Metadata,
	)
	return &i, err
}

const DeleteExpiredToolCache = `-- name: DeleteExpiredToolCache :exec
DELETE FROM tool_cache
WHERE expires_at <= NOW()
`

func (q *Queries) DeleteExpiredToolCache(ctx context.Context) error {
	_, err := q.db.Exec(ctx, DeleteExpiredToolCache)
	return err
}

const DeleteExpiredUserContext = `-- name: DeleteExpiredUserContext :exec
DELETE FROM user_context
WHERE expires_at IS NOT NULL AND expires_at <= NOW()
`

func (q *Queries) DeleteExpiredUserContext(ctx context.Context) error {
	_, err := q.db.Exec(ctx, DeleteExpiredUserContext)
	return err
}

const DeleteToolCacheByUser = `-- name: DeleteToolCacheByUser :exec
DELETE FROM tool_cache
WHERE user_id = $1::uuid
  AND (tool_name = $2 OR $2 IS NULL)
  AND (created_at < $3 OR $3 IS NULL)
`

type DeleteToolCacheByUserParams struct {
	Column1   pgtype.UUID `json:"column_1"`
	ToolName  string      `json:"tool_name"`
	CreatedAt time.Time   `json:"created_at"`
}

func (q *Queries) DeleteToolCacheByUser(ctx context.Context, arg DeleteToolCacheByUserParams) error {
	_, err := q.db.Exec(ctx, DeleteToolCacheByUser, arg.Column1, arg.ToolName, arg.CreatedAt)
	return err
}

const DeleteToolCacheEntry = `-- name: DeleteToolCacheEntry :exec
DELETE FROM tool_cache
WHERE id = $1
`

func (q *Queries) DeleteToolCacheEntry(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteToolCacheEntry, id)
	return err
}

const DeleteUserContext = `-- name: DeleteUserContext :exec
DELETE FROM user_context
WHERE user_id = $1::uuid AND context_type = $2 AND context_key = $3
`

type DeleteUserContextParams struct {
	Column1     pgtype.UUID `json:"column_1"`
	ContextType string      `json:"context_type"`
	ContextKey  string      `json:"context_key"`
}

func (q *Queries) DeleteUserContext(ctx context.Context, arg DeleteUserContextParams) error {
	_, err := q.db.Exec(ctx, DeleteUserContext, arg.Column1, arg.ContextType, arg.ContextKey)
	return err
}

const DeleteUserContextByType = `-- name: DeleteUserContextByType :exec
DELETE FROM user_context
WHERE user_id = $1::uuid AND context_type = $2
`

type DeleteUserContextByTypeParams struct {
	Column1     pgtype.UUID `json:"column_1"`
	ContextType string      `json:"context_type"`
}

func (q *Queries) DeleteUserContextByType(ctx context.Context, arg DeleteUserContextByTypeParams) error {
	_, err := q.db.Exec(ctx, DeleteUserContextByType, arg.Column1, arg.ContextType)
	return err
}

const DeleteUserPreference = `-- name: DeleteUserPreference :exec
DELETE FROM user_preferences
WHERE user_id = $1::uuid AND category = $2 AND preference_key = $3
`

type DeleteUserPreferenceParams struct {
	Column1       pgtype.UUID `json:"column_1"`
	Category      string      `json:"category"`
	PreferenceKey string      `json:"preference_key"`
}

func (q *Queries) DeleteUserPreference(ctx context.Context, arg DeleteUserPreferenceParams) error {
	_, err := q.db.Exec(ctx, DeleteUserPreference, arg.Column1, arg.Category, arg.PreferenceKey)
	return err
}

const DeleteUserPreferencesByCategory = `-- name: DeleteUserPreferencesByCategory :exec
DELETE FROM user_preferences
WHERE user_id = $1::uuid AND category = $2
`

type DeleteUserPreferencesByCategoryParams struct {
	Column1  pgtype.UUID `json:"column_1"`
	Category string      `json:"category"`
}

func (q *Queries) DeleteUserPreferencesByCategory(ctx context.Context, arg DeleteUserPreferencesByCategoryParams) error {
	_, err := q.db.Exec(ctx, DeleteUserPreferencesByCategory, arg.Column1, arg.Category)
	return err
}

const GetAllUserContext = `-- name: GetAllUserContext :many
SELECT id, user_id, context_type, context_key, context_value, importance, created_at, updated_at, expires_at, metadata FROM user_context
WHERE user_id = $1::uuid
  AND (expires_at IS NULL OR expires_at > NOW())
ORDER BY context_type, importance DESC
`

func (q *Queries) GetAllUserContext(ctx context.Context, dollar_1 pgtype.UUID) ([]*UserContext, error) {
	rows, err := q.db.Query(ctx, GetAllUserContext, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*UserContext{}
	for rows.Next() {
		var i UserContext
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.ContextType,
			&i.ContextKey,
			&i.ContextValue,
			&i.Importance,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ExpiresAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetAllUserPreferences = `-- name: GetAllUserPreferences :many
SELECT id, user_id, category, preference_key, preference_value, value_type, description, created_at, updated_at, metadata FROM user_preferences
WHERE user_id = $1::uuid
ORDER BY category, preference_key
`

func (q *Queries) GetAllUserPreferences(ctx context.Context, dollar_1 pgtype.UUID) ([]*UserPreference, error) {
	rows, err := q.db.Query(ctx, GetAllUserPreferences, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*UserPreference{}
	for rows.Next() {
		var i UserPreference
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Category,
			&i.PreferenceKey,
			&i.PreferenceValue,
			&i.ValueType,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetToolCacheByUser = `-- name: GetToolCacheByUser :many
SELECT id, user_id, tool_name, input_hash, input_data, output_data, execution_time_ms, success, error_message, hit_count, last_hit, created_at, expires_at, metadata FROM tool_cache
WHERE user_id = $1::uuid
  AND (tool_name = $2 OR $2 IS NULL)
  AND expires_at > NOW()
ORDER BY last_hit DESC
LIMIT $3 OFFSET $4
`

type GetToolCacheByUserParams struct {
	Column1  pgtype.UUID `json:"column_1"`
	ToolName string      `json:"tool_name"`
	Limit    int32       `json:"limit"`
	Offset   int32       `json:"offset"`
}

func (q *Queries) GetToolCacheByUser(ctx context.Context, arg GetToolCacheByUserParams) ([]*ToolCache, error) {
	rows, err := q.db.Query(ctx, GetToolCacheByUser,
		arg.Column1,
		arg.ToolName,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ToolCache{}
	for rows.Next() {
		var i ToolCache
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.ToolName,
			&i.InputHash,
			&i.InputData,
			&i.OutputData,
			&i.ExecutionTimeMs,
			&i.Success,
			&i.ErrorMessage,
			&i.HitCount,
			&i.LastHit,
			&i.CreatedAt,
			&i.ExpiresAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetToolCacheEntry = `-- name: GetToolCacheEntry :one
SELECT id, user_id, tool_name, input_hash, input_data, output_data, execution_time_ms, success, error_message, hit_count, last_hit, created_at, expires_at, metadata FROM tool_cache
WHERE user_id = $1::uuid AND tool_name = $2 AND input_hash = $3
  AND expires_at > NOW()
`

type GetToolCacheEntryParams struct {
	Column1   pgtype.UUID `json:"column_1"`
	ToolName  string      `json:"tool_name"`
	InputHash string      `json:"input_hash"`
}

func (q *Queries) GetToolCacheEntry(ctx context.Context, arg GetToolCacheEntryParams) (*ToolCache, error) {
	row := q.db.QueryRow(ctx, GetToolCacheEntry, arg.Column1, arg.ToolName, arg.InputHash)
	var i ToolCache
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.ToolName,
		&i.InputHash,
		&i.InputData,
		&i.OutputData,
		&i.ExecutionTimeMs,
		&i.Success,
		&i.ErrorMessage,
		&i.HitCount,
		&i.LastHit,
		&i.CreatedAt,
		&i.ExpiresAt,
		&i.Metadata,
	)
	return &i, err
}

const GetToolCacheStats = `-- name: GetToolCacheStats :one
SELECT
    tool_name,
    COUNT(*) as cache_entries,
    SUM(hit_count) as total_hits,
    AVG(execution_time_ms) as avg_execution_time_ms,
    MAX(last_hit) as last_used
FROM tool_cache
WHERE user_id = $1::uuid
  AND expires_at > NOW()
GROUP BY tool_name
`

type GetToolCacheStatsRow struct {
	ToolName           string      `json:"tool_name"`
	CacheEntries       int64       `json:"cache_entries"`
	TotalHits          int64       `json:"total_hits"`
	AvgExecutionTimeMs float64     `json:"avg_execution_time_ms"`
	LastUsed           interface{} `json:"last_used"`
}

func (q *Queries) GetToolCacheStats(ctx context.Context, dollar_1 pgtype.UUID) (*GetToolCacheStatsRow, error) {
	row := q.db.QueryRow(ctx, GetToolCacheStats, dollar_1)
	var i GetToolCacheStatsRow
	err := row.Scan(
		&i.ToolName,
		&i.CacheEntries,
		&i.TotalHits,
		&i.AvgExecutionTimeMs,
		&i.LastUsed,
	)
	return &i, err
}

const GetUserContext = `-- name: GetUserContext :one
SELECT id, user_id, context_type, context_key, context_value, importance, created_at, updated_at, expires_at, metadata FROM user_context
WHERE user_id = $1::uuid AND context_type = $2 AND context_key = $3
  AND (expires_at IS NULL OR expires_at > NOW())
`

type GetUserContextParams struct {
	Column1     pgtype.UUID `json:"column_1"`
	ContextType string      `json:"context_type"`
	ContextKey  string      `json:"context_key"`
}

func (q *Queries) GetUserContext(ctx context.Context, arg GetUserContextParams) (*UserContext, error) {
	row := q.db.QueryRow(ctx, GetUserContext, arg.Column1, arg.ContextType, arg.ContextKey)
	var i UserContext
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.ContextType,
		&i.ContextKey,
		&i.ContextValue,
		&i.Importance,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ExpiresAt,
		&i.Metadata,
	)
	return &i, err
}

const GetUserContextByType = `-- name: GetUserContextByType :many
SELECT id, user_id, context_type, context_key, context_value, importance, created_at, updated_at, expires_at, metadata FROM user_context
WHERE user_id = $1::uuid AND context_type = $2
  AND (expires_at IS NULL OR expires_at > NOW())
ORDER BY importance DESC, updated_at DESC
`

type GetUserContextByTypeParams struct {
	Column1     pgtype.UUID `json:"column_1"`
	ContextType string      `json:"context_type"`
}

func (q *Queries) GetUserContextByType(ctx context.Context, arg GetUserContextByTypeParams) ([]*UserContext, error) {
	rows, err := q.db.Query(ctx, GetUserContextByType, arg.Column1, arg.ContextType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*UserContext{}
	for rows.Next() {
		var i UserContext
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.ContextType,
			&i.ContextKey,
			&i.ContextValue,
			&i.Importance,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ExpiresAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetUserPreference = `-- name: GetUserPreference :one
SELECT id, user_id, category, preference_key, preference_value, value_type, description, created_at, updated_at, metadata FROM user_preferences
WHERE user_id = $1::uuid AND category = $2 AND preference_key = $3
`

type GetUserPreferenceParams struct {
	Column1       pgtype.UUID `json:"column_1"`
	Category      string      `json:"category"`
	PreferenceKey string      `json:"preference_key"`
}

func (q *Queries) GetUserPreference(ctx context.Context, arg GetUserPreferenceParams) (*UserPreference, error) {
	row := q.db.QueryRow(ctx, GetUserPreference, arg.Column1, arg.Category, arg.PreferenceKey)
	var i UserPreference
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Category,
		&i.PreferenceKey,
		&i.PreferenceValue,
		&i.ValueType,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Metadata,
	)
	return &i, err
}

const GetUserPreferencesByCategory = `-- name: GetUserPreferencesByCategory :many
SELECT id, user_id, category, preference_key, preference_value, value_type, description, created_at, updated_at, metadata FROM user_preferences
WHERE user_id = $1::uuid AND category = $2
ORDER BY preference_key
`

type GetUserPreferencesByCategoryParams struct {
	Column1  pgtype.UUID `json:"column_1"`
	Category string      `json:"category"`
}

func (q *Queries) GetUserPreferencesByCategory(ctx context.Context, arg GetUserPreferencesByCategoryParams) ([]*UserPreference, error) {
	rows, err := q.db.Query(ctx, GetUserPreferencesByCategory, arg.Column1, arg.Category)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*UserPreference{}
	for rows.Next() {
		var i UserPreference
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Category,
			&i.PreferenceKey,
			&i.PreferenceValue,
			&i.ValueType,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateToolCacheHit = `-- name: UpdateToolCacheHit :one
UPDATE tool_cache
SET hit_count = hit_count + 1,
    last_hit = NOW()
WHERE id = $1
RETURNING id, user_id, tool_name, input_hash, input_data, output_data, execution_time_ms, success, error_message, hit_count, last_hit, created_at, expires_at, metadata
`

func (q *Queries) UpdateToolCacheHit(ctx context.Context, id pgtype.UUID) (*ToolCache, error) {
	row := q.db.QueryRow(ctx, UpdateToolCacheHit, id)
	var i ToolCache
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.ToolName,
		&i.InputHash,
		&i.InputData,
		&i.OutputData,
		&i.ExecutionTimeMs,
		&i.Success,
		&i.ErrorMessage,
		&i.HitCount,
		&i.LastHit,
		&i.CreatedAt,
		&i.ExpiresAt,
		&i.Metadata,
	)
	return &i, err
}

const UpdateUserContext = `-- name: UpdateUserContext :one
UPDATE user_context
SET context_value = $4,
    importance = $5,
    expires_at = $6,
    metadata = $7
WHERE user_id = $1::uuid AND context_type = $2 AND context_key = $3
RETURNING id, user_id, context_type, context_key, context_value, importance, created_at, updated_at, expires_at, metadata
`

type UpdateUserContextParams struct {
	Column1      pgtype.UUID        `json:"column_1"`
	ContextType  string             `json:"context_type"`
	ContextKey   string             `json:"context_key"`
	ContextValue []byte             `json:"context_value"`
	Importance   pgtype.Numeric     `json:"importance"`
	ExpiresAt    pgtype.Timestamptz `json:"expires_at"`
	Metadata     json.RawMessage    `json:"metadata"`
}

func (q *Queries) UpdateUserContext(ctx context.Context, arg UpdateUserContextParams) (*UserContext, error) {
	row := q.db.QueryRow(ctx, UpdateUserContext,
		arg.Column1,
		arg.ContextType,
		arg.ContextKey,
		arg.ContextValue,
		arg.Importance,
		arg.ExpiresAt,
		arg.Metadata,
	)
	var i UserContext
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.ContextType,
		&i.ContextKey,
		&i.ContextValue,
		&i.Importance,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ExpiresAt,
		&i.Metadata,
	)
	return &i, err
}

const UpdateUserPreference = `-- name: UpdateUserPreference :one
UPDATE user_preferences
SET preference_value = $4,
    value_type = $5,
    description = $6,
    metadata = $7
WHERE user_id = $1::uuid AND category = $2 AND preference_key = $3
RETURNING id, user_id, category, preference_key, preference_value, value_type, description, created_at, updated_at, metadata
`

type UpdateUserPreferenceParams struct {
	Column1         pgtype.UUID     `json:"column_1"`
	Category        string          `json:"category"`
	PreferenceKey   string          `json:"preference_key"`
	PreferenceValue []byte          `json:"preference_value"`
	ValueType       string          `json:"value_type"`
	Description     pgtype.Text     `json:"description"`
	Metadata        json.RawMessage `json:"metadata"`
}

func (q *Queries) UpdateUserPreference(ctx context.Context, arg UpdateUserPreferenceParams) (*UserPreference, error) {
	row := q.db.QueryRow(ctx, UpdateUserPreference,
		arg.Column1,
		arg.Category,
		arg.PreferenceKey,
		arg.PreferenceValue,
		arg.ValueType,
		arg.Description,
		arg.Metadata,
	)
	var i UserPreference
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Category,
		&i.PreferenceKey,
		&i.PreferenceValue,
		&i.ValueType,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Metadata,
	)
	return &i, err
}
