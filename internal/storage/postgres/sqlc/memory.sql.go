// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: memory.sql

package sqlc

import (
	"context"
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const CreateMemoryEntry = `-- name: CreateMemoryEntry :one
INSERT INTO memory_entries (
    memory_type,
    user_id,
    session_id,
    content,
    importance,
    access_count,
    last_access,
    expires_at,
    metadata
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9
) RETURNING id, memory_type, user_id, session_id, content, importance, access_count, last_access, created_at, expires_at, metadata
`

type CreateMemoryEntryParams struct {
	MemoryType  string             `json:"memory_type"`
	UserID      string             `json:"user_id"`
	SessionID   pgtype.Text        `json:"session_id"`
	Content     string             `json:"content"`
	Importance  pgtype.Numeric     `json:"importance"`
	AccessCount pgtype.Int4        `json:"access_count"`
	LastAccess  pgtype.Timestamptz `json:"last_access"`
	ExpiresAt   pgtype.Timestamptz `json:"expires_at"`
	Metadata    json.RawMessage    `json:"metadata"`
}

func (q *Queries) CreateMemoryEntry(ctx context.Context, arg CreateMemoryEntryParams) (*MemoryEntry, error) {
	row := q.db.QueryRow(ctx, CreateMemoryEntry,
		arg.MemoryType,
		arg.UserID,
		arg.SessionID,
		arg.Content,
		arg.Importance,
		arg.AccessCount,
		arg.LastAccess,
		arg.ExpiresAt,
		arg.Metadata,
	)
	var i MemoryEntry
	err := row.Scan(
		&i.ID,
		&i.MemoryType,
		&i.UserID,
		&i.SessionID,
		&i.Content,
		&i.Importance,
		&i.AccessCount,
		&i.LastAccess,
		&i.CreatedAt,
		&i.ExpiresAt,
		&i.Metadata,
	)
	return &i, err
}

const DeleteExpiredMemoryEntries = `-- name: DeleteExpiredMemoryEntries :exec
DELETE FROM memory_entries
WHERE expires_at IS NOT NULL AND expires_at <= NOW()
`

func (q *Queries) DeleteExpiredMemoryEntries(ctx context.Context) error {
	_, err := q.db.Exec(ctx, DeleteExpiredMemoryEntries)
	return err
}

const DeleteMemoryEntriesByUser = `-- name: DeleteMemoryEntriesByUser :exec
DELETE FROM memory_entries
WHERE user_id = $1
  AND (memory_type = ANY($2::text[]) OR $2 IS NULL)
  AND (created_at < $3 OR $3 IS NULL)
`

type DeleteMemoryEntriesByUserParams struct {
	UserID    string    `json:"user_id"`
	Column2   []string  `json:"column_2"`
	CreatedAt time.Time `json:"created_at"`
}

func (q *Queries) DeleteMemoryEntriesByUser(ctx context.Context, arg DeleteMemoryEntriesByUserParams) error {
	_, err := q.db.Exec(ctx, DeleteMemoryEntriesByUser, arg.UserID, arg.Column2, arg.CreatedAt)
	return err
}

const DeleteMemoryEntry = `-- name: DeleteMemoryEntry :exec
DELETE FROM memory_entries
WHERE id = $1
`

func (q *Queries) DeleteMemoryEntry(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteMemoryEntry, id)
	return err
}

const GetMemoryEntriesBySession = `-- name: GetMemoryEntriesBySession :many
SELECT id, memory_type, user_id, session_id, content, importance, access_count, last_access, created_at, expires_at, metadata FROM memory_entries
WHERE user_id = $1 
  AND session_id = $2
  AND (expires_at IS NULL OR expires_at > NOW())
ORDER BY last_access DESC
LIMIT $3 OFFSET $4
`

type GetMemoryEntriesBySessionParams struct {
	UserID    string      `json:"user_id"`
	SessionID pgtype.Text `json:"session_id"`
	Limit     int32       `json:"limit"`
	Offset    int32       `json:"offset"`
}

func (q *Queries) GetMemoryEntriesBySession(ctx context.Context, arg GetMemoryEntriesBySessionParams) ([]*MemoryEntry, error) {
	rows, err := q.db.Query(ctx, GetMemoryEntriesBySession,
		arg.UserID,
		arg.SessionID,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*MemoryEntry{}
	for rows.Next() {
		var i MemoryEntry
		if err := rows.Scan(
			&i.ID,
			&i.MemoryType,
			&i.UserID,
			&i.SessionID,
			&i.Content,
			&i.Importance,
			&i.AccessCount,
			&i.LastAccess,
			&i.CreatedAt,
			&i.ExpiresAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetMemoryEntriesByUser = `-- name: GetMemoryEntriesByUser :many
SELECT id, memory_type, user_id, session_id, content, importance, access_count, last_access, created_at, expires_at, metadata FROM memory_entries
WHERE user_id = $1
  AND (memory_type = ANY($2::text[]) OR $2 IS NULL)
  AND (expires_at IS NULL OR expires_at > NOW())
ORDER BY importance DESC, last_access DESC
LIMIT $3 OFFSET $4
`

type GetMemoryEntriesByUserParams struct {
	UserID  string   `json:"user_id"`
	Column2 []string `json:"column_2"`
	Limit   int32    `json:"limit"`
	Offset  int32    `json:"offset"`
}

func (q *Queries) GetMemoryEntriesByUser(ctx context.Context, arg GetMemoryEntriesByUserParams) ([]*MemoryEntry, error) {
	rows, err := q.db.Query(ctx, GetMemoryEntriesByUser,
		arg.UserID,
		arg.Column2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*MemoryEntry{}
	for rows.Next() {
		var i MemoryEntry
		if err := rows.Scan(
			&i.ID,
			&i.MemoryType,
			&i.UserID,
			&i.SessionID,
			&i.Content,
			&i.Importance,
			&i.AccessCount,
			&i.LastAccess,
			&i.CreatedAt,
			&i.ExpiresAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetMemoryEntry = `-- name: GetMemoryEntry :one
SELECT id, memory_type, user_id, session_id, content, importance, access_count, last_access, created_at, expires_at, metadata FROM memory_entries
WHERE id = $1
`

func (q *Queries) GetMemoryEntry(ctx context.Context, id pgtype.UUID) (*MemoryEntry, error) {
	row := q.db.QueryRow(ctx, GetMemoryEntry, id)
	var i MemoryEntry
	err := row.Scan(
		&i.ID,
		&i.MemoryType,
		&i.UserID,
		&i.SessionID,
		&i.Content,
		&i.Importance,
		&i.AccessCount,
		&i.LastAccess,
		&i.CreatedAt,
		&i.ExpiresAt,
		&i.Metadata,
	)
	return &i, err
}

const GetMemoryStats = `-- name: GetMemoryStats :one
SELECT 
    memory_type,
    COUNT(*) as entry_count,
    AVG(importance) as avg_importance,
    MIN(created_at) as oldest_entry,
    MAX(created_at) as newest_entry
FROM memory_entries
WHERE user_id = $1
  AND (expires_at IS NULL OR expires_at > NOW())
GROUP BY memory_type
`

type GetMemoryStatsRow struct {
	MemoryType    string      `json:"memory_type"`
	EntryCount    int64       `json:"entry_count"`
	AvgImportance float64     `json:"avg_importance"`
	OldestEntry   interface{} `json:"oldest_entry"`
	NewestEntry   interface{} `json:"newest_entry"`
}

func (q *Queries) GetMemoryStats(ctx context.Context, userID string) (*GetMemoryStatsRow, error) {
	row := q.db.QueryRow(ctx, GetMemoryStats, userID)
	var i GetMemoryStatsRow
	err := row.Scan(
		&i.MemoryType,
		&i.EntryCount,
		&i.AvgImportance,
		&i.OldestEntry,
		&i.NewestEntry,
	)
	return &i, err
}

const IncrementMemoryAccess = `-- name: IncrementMemoryAccess :one
UPDATE memory_entries
SET access_count = access_count + 1,
    last_access = NOW()
WHERE id = $1
RETURNING id, memory_type, user_id, session_id, content, importance, access_count, last_access, created_at, expires_at, metadata
`

func (q *Queries) IncrementMemoryAccess(ctx context.Context, id pgtype.UUID) (*MemoryEntry, error) {
	row := q.db.QueryRow(ctx, IncrementMemoryAccess, id)
	var i MemoryEntry
	err := row.Scan(
		&i.ID,
		&i.MemoryType,
		&i.UserID,
		&i.SessionID,
		&i.Content,
		&i.Importance,
		&i.AccessCount,
		&i.LastAccess,
		&i.CreatedAt,
		&i.ExpiresAt,
		&i.Metadata,
	)
	return &i, err
}

const SearchMemoryEntries = `-- name: SearchMemoryEntries :many
SELECT id, memory_type, user_id, session_id, content, importance, access_count, last_access, created_at, expires_at, metadata FROM memory_entries
WHERE user_id = $1
  AND (memory_type = ANY($2::text[]) OR $2 IS NULL)
  AND content ILIKE '%' || $3 || '%'
  AND (expires_at IS NULL OR expires_at > NOW())
  AND importance >= $4
ORDER BY importance DESC, last_access DESC
LIMIT $5 OFFSET $6
`

type SearchMemoryEntriesParams struct {
	UserID     string         `json:"user_id"`
	Column2    []string       `json:"column_2"`
	Column3    pgtype.Text    `json:"column_3"`
	Importance pgtype.Numeric `json:"importance"`
	Limit      int32          `json:"limit"`
	Offset     int32          `json:"offset"`
}

func (q *Queries) SearchMemoryEntries(ctx context.Context, arg SearchMemoryEntriesParams) ([]*MemoryEntry, error) {
	rows, err := q.db.Query(ctx, SearchMemoryEntries,
		arg.UserID,
		arg.Column2,
		arg.Column3,
		arg.Importance,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*MemoryEntry{}
	for rows.Next() {
		var i MemoryEntry
		if err := rows.Scan(
			&i.ID,
			&i.MemoryType,
			&i.UserID,
			&i.SessionID,
			&i.Content,
			&i.Importance,
			&i.AccessCount,
			&i.LastAccess,
			&i.CreatedAt,
			&i.ExpiresAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateMemoryEntry = `-- name: UpdateMemoryEntry :one
UPDATE memory_entries
SET content = $2,
    importance = $3,
    access_count = $4,
    last_access = $5,
    expires_at = $6,
    metadata = $7
WHERE id = $1
RETURNING id, memory_type, user_id, session_id, content, importance, access_count, last_access, created_at, expires_at, metadata
`

type UpdateMemoryEntryParams struct {
	ID          pgtype.UUID        `json:"id"`
	Content     string             `json:"content"`
	Importance  pgtype.Numeric     `json:"importance"`
	AccessCount pgtype.Int4        `json:"access_count"`
	LastAccess  pgtype.Timestamptz `json:"last_access"`
	ExpiresAt   pgtype.Timestamptz `json:"expires_at"`
	Metadata    json.RawMessage    `json:"metadata"`
}

func (q *Queries) UpdateMemoryEntry(ctx context.Context, arg UpdateMemoryEntryParams) (*MemoryEntry, error) {
	row := q.db.QueryRow(ctx, UpdateMemoryEntry,
		arg.ID,
		arg.Content,
		arg.Importance,
		arg.AccessCount,
		arg.LastAccess,
		arg.ExpiresAt,
		arg.Metadata,
	)
	var i MemoryEntry
	err := row.Scan(
		&i.ID,
		&i.MemoryType,
		&i.UserID,
		&i.SessionID,
		&i.Content,
		&i.Importance,
		&i.AccessCount,
		&i.LastAccess,
		&i.CreatedAt,
		&i.ExpiresAt,
		&i.Metadata,
	)
	return &i, err
}
