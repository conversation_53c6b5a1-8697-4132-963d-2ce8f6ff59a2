// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

type Querier interface {
	// Agent execution queries
	CreateAgentExecution(ctx context.Context, arg CreateAgentExecutionParams) (*AgentExecution, error)
	// Chain execution queries
	CreateChainExecution(ctx context.Context, arg CreateChainExecutionParams) (*ChainExecution, error)
	CreateConversation(ctx context.Context, arg CreateConversationParams) (*Conversation, error)
	CreateEmbedding(ctx context.Context, arg CreateEmbeddingParams) (*Embedding, error)
	CreateMemoryEntry(ctx context.Context, arg CreateMemoryEntryParams) (*MemoryEntry, error)
	CreateMessage(ctx context.Context, arg CreateMessageParams) (*Message, error)
	// Tool cache queries
	CreateToolCacheEntry(ctx context.Context, arg CreateToolCacheEntryParams) (*ToolCache, error)
	// User context queries
	CreateUserContext(ctx context.Context, arg CreateUserContextParams) (*UserContext, error)
	// User preferences queries
	CreateUserPreference(ctx context.Context, arg CreateUserPreferenceParams) (*UserPreference, error)
	DeleteAgentExecution(ctx context.Context, id pgtype.UUID) error
	DeleteChainExecution(ctx context.Context, id pgtype.UUID) error
	DeleteConversation(ctx context.Context, id pgtype.UUID) error
	DeleteEmbedding(ctx context.Context, arg DeleteEmbeddingParams) error
	DeleteEmbeddingByID(ctx context.Context, id pgtype.UUID) error
	DeleteExpiredMemoryEntries(ctx context.Context) error
	DeleteExpiredToolCache(ctx context.Context) error
	DeleteExpiredUserContext(ctx context.Context) error
	DeleteMemoryEntriesByUser(ctx context.Context, arg DeleteMemoryEntriesByUserParams) error
	DeleteMemoryEntry(ctx context.Context, id pgtype.UUID) error
	DeleteMessage(ctx context.Context, id pgtype.UUID) error
	DeleteMessagesByConversation(ctx context.Context, conversationID pgtype.UUID) error
	DeleteToolCacheByUser(ctx context.Context, arg DeleteToolCacheByUserParams) error
	DeleteToolCacheEntry(ctx context.Context, id pgtype.UUID) error
	DeleteUserContext(ctx context.Context, arg DeleteUserContextParams) error
	DeleteUserContextByType(ctx context.Context, arg DeleteUserContextByTypeParams) error
	DeleteUserPreference(ctx context.Context, arg DeleteUserPreferenceParams) error
	DeleteUserPreferencesByCategory(ctx context.Context, arg DeleteUserPreferencesByCategoryParams) error
	GetAgentExecution(ctx context.Context, id pgtype.UUID) (*AgentExecution, error)
	GetAgentExecutionStats(ctx context.Context, arg GetAgentExecutionStatsParams) (*GetAgentExecutionStatsRow, error)
	GetAgentExecutionsByUser(ctx context.Context, arg GetAgentExecutionsByUserParams) ([]*AgentExecution, error)
	GetAllEmbeddingCount(ctx context.Context) (int64, error)
	GetAllMessagesByConversation(ctx context.Context, conversationID pgtype.UUID) ([]*Message, error)
	GetAllUserContext(ctx context.Context, userID string) ([]*UserContext, error)
	GetAllUserPreferences(ctx context.Context, userID string) ([]*UserPreference, error)
	GetChainExecution(ctx context.Context, id pgtype.UUID) (*ChainExecution, error)
	GetChainExecutionStats(ctx context.Context, arg GetChainExecutionStatsParams) (*GetChainExecutionStatsRow, error)
	GetChainExecutionsByUser(ctx context.Context, arg GetChainExecutionsByUserParams) ([]*ChainExecution, error)
	GetConversation(ctx context.Context, id pgtype.UUID) (*Conversation, error)
	GetConversationCount(ctx context.Context, userID string) (int64, error)
	GetConversationsByUser(ctx context.Context, arg GetConversationsByUserParams) ([]*Conversation, error)
	GetEmbedding(ctx context.Context, arg GetEmbeddingParams) (*Embedding, error)
	GetEmbeddingByID(ctx context.Context, id pgtype.UUID) (*Embedding, error)
	GetEmbeddingCount(ctx context.Context, contentType string) (int64, error)
	GetEmbeddingsByType(ctx context.Context, arg GetEmbeddingsByTypeParams) ([]*Embedding, error)
	GetExecutionTrends(ctx context.Context, arg GetExecutionTrendsParams) ([]*GetExecutionTrendsRow, error)
	GetMemoryEntriesBySession(ctx context.Context, arg GetMemoryEntriesBySessionParams) ([]*MemoryEntry, error)
	GetMemoryEntriesByUser(ctx context.Context, arg GetMemoryEntriesByUserParams) ([]*MemoryEntry, error)
	GetMemoryEntry(ctx context.Context, id pgtype.UUID) (*MemoryEntry, error)
	GetMemoryStats(ctx context.Context, userID string) (*GetMemoryStatsRow, error)
	GetMessage(ctx context.Context, id pgtype.UUID) (*Message, error)
	GetMessageCount(ctx context.Context, conversationID pgtype.UUID) (int64, error)
	GetMessagesByConversation(ctx context.Context, arg GetMessagesByConversationParams) ([]*Message, error)
	GetMessagesByRole(ctx context.Context, arg GetMessagesByRoleParams) ([]*Message, error)
	GetRecentAgentExecutions(ctx context.Context, arg GetRecentAgentExecutionsParams) ([]*AgentExecution, error)
	GetRecentChainExecutions(ctx context.Context, arg GetRecentChainExecutionsParams) ([]*ChainExecution, error)
	GetRecentConversations(ctx context.Context, arg GetRecentConversationsParams) ([]*Conversation, error)
	GetRecentMessages(ctx context.Context, arg GetRecentMessagesParams) ([]*Message, error)
	GetToolCacheByUser(ctx context.Context, arg GetToolCacheByUserParams) ([]*ToolCache, error)
	GetToolCacheEntry(ctx context.Context, arg GetToolCacheEntryParams) (*ToolCache, error)
	GetToolCacheStats(ctx context.Context, userID string) (*GetToolCacheStatsRow, error)
	GetUserContext(ctx context.Context, arg GetUserContextParams) (*UserContext, error)
	GetUserContextByType(ctx context.Context, arg GetUserContextByTypeParams) ([]*UserContext, error)
	// Combined analytics queries
	GetUserExecutionSummary(ctx context.Context, arg GetUserExecutionSummaryParams) (*GetUserExecutionSummaryRow, error)
	GetUserPreference(ctx context.Context, arg GetUserPreferenceParams) (*UserPreference, error)
	GetUserPreferencesByCategory(ctx context.Context, arg GetUserPreferencesByCategoryParams) ([]*UserPreference, error)
	IncrementMemoryAccess(ctx context.Context, id pgtype.UUID) (*MemoryEntry, error)
	SearchConversations(ctx context.Context, arg SearchConversationsParams) ([]*Conversation, error)
	SearchMemoryEntries(ctx context.Context, arg SearchMemoryEntriesParams) ([]*MemoryEntry, error)
	SearchMessages(ctx context.Context, arg SearchMessagesParams) ([]*Message, error)
	SearchSimilarEmbeddings(ctx context.Context, arg SearchSimilarEmbeddingsParams) ([]*SearchSimilarEmbeddingsRow, error)
	SearchSimilarEmbeddingsAllTypes(ctx context.Context, arg SearchSimilarEmbeddingsAllTypesParams) ([]*SearchSimilarEmbeddingsAllTypesRow, error)
	UpdateConversation(ctx context.Context, arg UpdateConversationParams) (*Conversation, error)
	UpdateEmbedding(ctx context.Context, arg UpdateEmbeddingParams) (*Embedding, error)
	UpdateMemoryEntry(ctx context.Context, arg UpdateMemoryEntryParams) (*MemoryEntry, error)
	UpdateMessage(ctx context.Context, arg UpdateMessageParams) (*Message, error)
	UpdateToolCacheHit(ctx context.Context, id pgtype.UUID) (*ToolCache, error)
	UpdateUserContext(ctx context.Context, arg UpdateUserContextParams) (*UserContext, error)
	UpdateUserPreference(ctx context.Context, arg UpdateUserPreferenceParams) (*UserPreference, error)
}

var _ Querier = (*Queries)(nil)
