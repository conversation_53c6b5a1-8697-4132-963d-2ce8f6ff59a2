// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

type Querier interface {
	CreateConversation(ctx context.Context, arg CreateConversationParams) (*Conversation, error)
	CreateEmbedding(ctx context.Context, arg CreateEmbeddingParams) (*Embedding, error)
	CreateMessage(ctx context.Context, arg CreateMessageParams) (*Message, error)
	DeleteConversation(ctx context.Context, id pgtype.UUID) error
	DeleteEmbedding(ctx context.Context, arg DeleteEmbeddingParams) error
	DeleteEmbeddingByID(ctx context.Context, id pgtype.UUID) error
	DeleteMessage(ctx context.Context, id pgtype.UUID) error
	DeleteMessagesByConversation(ctx context.Context, conversationID pgtype.UUID) error
	GetAllEmbeddingCount(ctx context.Context) (int64, error)
	GetAllMessagesByConversation(ctx context.Context, conversationID pgtype.UUID) ([]*Message, error)
	GetConversation(ctx context.Context, id pgtype.UUID) (*Conversation, error)
	GetConversationCount(ctx context.Context, userID string) (int64, error)
	GetConversationsByUser(ctx context.Context, arg GetConversationsByUserParams) ([]*Conversation, error)
	GetEmbedding(ctx context.Context, arg GetEmbeddingParams) (*Embedding, error)
	GetEmbeddingByID(ctx context.Context, id pgtype.UUID) (*Embedding, error)
	GetEmbeddingCount(ctx context.Context, contentType string) (int64, error)
	GetEmbeddingsByType(ctx context.Context, arg GetEmbeddingsByTypeParams) ([]*Embedding, error)
	GetMessage(ctx context.Context, id pgtype.UUID) (*Message, error)
	GetMessageCount(ctx context.Context, conversationID pgtype.UUID) (int64, error)
	GetMessagesByConversation(ctx context.Context, arg GetMessagesByConversationParams) ([]*Message, error)
	GetMessagesByRole(ctx context.Context, arg GetMessagesByRoleParams) ([]*Message, error)
	GetRecentConversations(ctx context.Context, arg GetRecentConversationsParams) ([]*Conversation, error)
	GetRecentMessages(ctx context.Context, arg GetRecentMessagesParams) ([]*Message, error)
	SearchConversations(ctx context.Context, arg SearchConversationsParams) ([]*Conversation, error)
	SearchMessages(ctx context.Context, arg SearchMessagesParams) ([]*Message, error)
	SearchSimilarEmbeddings(ctx context.Context, arg SearchSimilarEmbeddingsParams) ([]*SearchSimilarEmbeddingsRow, error)
	SearchSimilarEmbeddingsAllTypes(ctx context.Context, arg SearchSimilarEmbeddingsAllTypesParams) ([]*SearchSimilarEmbeddingsAllTypesRow, error)
	UpdateConversation(ctx context.Context, arg UpdateConversationParams) (*Conversation, error)
	UpdateEmbedding(ctx context.Context, arg UpdateEmbeddingParams) (*Embedding, error)
	UpdateMessage(ctx context.Context, arg UpdateMessageParams) (*Message, error)
}

var _ Querier = (*Queries)(nil)
