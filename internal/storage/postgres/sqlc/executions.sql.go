// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: executions.sql

package sqlc

import (
	"context"
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const CreateAgentExecution = `-- name: CreateAgentExecution :one

INSERT INTO agent_executions (
    agent_type,
    user_id,
    query,
    response,
    steps,
    execution_time_ms,
    success,
    error_message,
    metadata
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9
) RETURNING id, agent_type, user_id, query, response, steps, execution_time_ms, success, error_message, created_at, metadata
`

type CreateAgentExecutionParams struct {
	AgentType       string          `json:"agent_type"`
	UserID          string          `json:"user_id"`
	Query           string          `json:"query"`
	Response        pgtype.Text     `json:"response"`
	Steps           []byte          `json:"steps"`
	ExecutionTimeMs pgtype.Int4     `json:"execution_time_ms"`
	Success         pgtype.Bool     `json:"success"`
	ErrorMessage    pgtype.Text     `json:"error_message"`
	Metadata        json.RawMessage `json:"metadata"`
}

// Agent execution queries
func (q *Queries) CreateAgentExecution(ctx context.Context, arg CreateAgentExecutionParams) (*AgentExecution, error) {
	row := q.db.QueryRow(ctx, CreateAgentExecution,
		arg.AgentType,
		arg.UserID,
		arg.Query,
		arg.Response,
		arg.Steps,
		arg.ExecutionTimeMs,
		arg.Success,
		arg.ErrorMessage,
		arg.Metadata,
	)
	var i AgentExecution
	err := row.Scan(
		&i.ID,
		&i.AgentType,
		&i.UserID,
		&i.Query,
		&i.Response,
		&i.Steps,
		&i.ExecutionTimeMs,
		&i.Success,
		&i.ErrorMessage,
		&i.CreatedAt,
		&i.Metadata,
	)
	return &i, err
}

const CreateChainExecution = `-- name: CreateChainExecution :one

INSERT INTO chain_executions (
    chain_type,
    user_id,
    input,
    output,
    steps,
    execution_time_ms,
    tokens_used,
    success,
    error_message,
    metadata
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
) RETURNING id, chain_type, user_id, input, output, steps, execution_time_ms, tokens_used, success, error_message, created_at, metadata
`

type CreateChainExecutionParams struct {
	ChainType       string          `json:"chain_type"`
	UserID          string          `json:"user_id"`
	Input           string          `json:"input"`
	Output          pgtype.Text     `json:"output"`
	Steps           []byte          `json:"steps"`
	ExecutionTimeMs pgtype.Int4     `json:"execution_time_ms"`
	TokensUsed      pgtype.Int4     `json:"tokens_used"`
	Success         pgtype.Bool     `json:"success"`
	ErrorMessage    pgtype.Text     `json:"error_message"`
	Metadata        json.RawMessage `json:"metadata"`
}

// Chain execution queries
func (q *Queries) CreateChainExecution(ctx context.Context, arg CreateChainExecutionParams) (*ChainExecution, error) {
	row := q.db.QueryRow(ctx, CreateChainExecution,
		arg.ChainType,
		arg.UserID,
		arg.Input,
		arg.Output,
		arg.Steps,
		arg.ExecutionTimeMs,
		arg.TokensUsed,
		arg.Success,
		arg.ErrorMessage,
		arg.Metadata,
	)
	var i ChainExecution
	err := row.Scan(
		&i.ID,
		&i.ChainType,
		&i.UserID,
		&i.Input,
		&i.Output,
		&i.Steps,
		&i.ExecutionTimeMs,
		&i.TokensUsed,
		&i.Success,
		&i.ErrorMessage,
		&i.CreatedAt,
		&i.Metadata,
	)
	return &i, err
}

const DeleteAgentExecution = `-- name: DeleteAgentExecution :exec
DELETE FROM agent_executions
WHERE id = $1
`

func (q *Queries) DeleteAgentExecution(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteAgentExecution, id)
	return err
}

const DeleteChainExecution = `-- name: DeleteChainExecution :exec
DELETE FROM chain_executions
WHERE id = $1
`

func (q *Queries) DeleteChainExecution(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteChainExecution, id)
	return err
}

const GetAgentExecution = `-- name: GetAgentExecution :one
SELECT id, agent_type, user_id, query, response, steps, execution_time_ms, success, error_message, created_at, metadata FROM agent_executions
WHERE id = $1
`

func (q *Queries) GetAgentExecution(ctx context.Context, id pgtype.UUID) (*AgentExecution, error) {
	row := q.db.QueryRow(ctx, GetAgentExecution, id)
	var i AgentExecution
	err := row.Scan(
		&i.ID,
		&i.AgentType,
		&i.UserID,
		&i.Query,
		&i.Response,
		&i.Steps,
		&i.ExecutionTimeMs,
		&i.Success,
		&i.ErrorMessage,
		&i.CreatedAt,
		&i.Metadata,
	)
	return &i, err
}

const GetAgentExecutionStats = `-- name: GetAgentExecutionStats :one
SELECT
    agent_type,
    COUNT(*) as total_executions,
    COUNT(*) FILTER (WHERE success = true) as successful_executions,
    AVG(execution_time_ms) as avg_execution_time_ms,
    MAX(created_at) as last_execution
FROM agent_executions
WHERE user_id = $1
  AND (agent_type = $2 OR $2 IS NULL)
  AND created_at >= $3
GROUP BY agent_type
`

type GetAgentExecutionStatsParams struct {
	UserID    string    `json:"user_id"`
	AgentType string    `json:"agent_type"`
	CreatedAt time.Time `json:"created_at"`
}

type GetAgentExecutionStatsRow struct {
	AgentType            string      `json:"agent_type"`
	TotalExecutions      int64       `json:"total_executions"`
	SuccessfulExecutions int64       `json:"successful_executions"`
	AvgExecutionTimeMs   float64     `json:"avg_execution_time_ms"`
	LastExecution        interface{} `json:"last_execution"`
}

func (q *Queries) GetAgentExecutionStats(ctx context.Context, arg GetAgentExecutionStatsParams) (*GetAgentExecutionStatsRow, error) {
	row := q.db.QueryRow(ctx, GetAgentExecutionStats, arg.UserID, arg.AgentType, arg.CreatedAt)
	var i GetAgentExecutionStatsRow
	err := row.Scan(
		&i.AgentType,
		&i.TotalExecutions,
		&i.SuccessfulExecutions,
		&i.AvgExecutionTimeMs,
		&i.LastExecution,
	)
	return &i, err
}

const GetAgentExecutionsByUser = `-- name: GetAgentExecutionsByUser :many
SELECT id, agent_type, user_id, query, response, steps, execution_time_ms, success, error_message, created_at, metadata FROM agent_executions
WHERE user_id = $1
  AND (agent_type = $2 OR $2 IS NULL)
ORDER BY created_at DESC
LIMIT $3 OFFSET $4
`

type GetAgentExecutionsByUserParams struct {
	UserID    string `json:"user_id"`
	AgentType string `json:"agent_type"`
	Limit     int32  `json:"limit"`
	Offset    int32  `json:"offset"`
}

func (q *Queries) GetAgentExecutionsByUser(ctx context.Context, arg GetAgentExecutionsByUserParams) ([]*AgentExecution, error) {
	rows, err := q.db.Query(ctx, GetAgentExecutionsByUser,
		arg.UserID,
		arg.AgentType,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*AgentExecution{}
	for rows.Next() {
		var i AgentExecution
		if err := rows.Scan(
			&i.ID,
			&i.AgentType,
			&i.UserID,
			&i.Query,
			&i.Response,
			&i.Steps,
			&i.ExecutionTimeMs,
			&i.Success,
			&i.ErrorMessage,
			&i.CreatedAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetChainExecution = `-- name: GetChainExecution :one
SELECT id, chain_type, user_id, input, output, steps, execution_time_ms, tokens_used, success, error_message, created_at, metadata FROM chain_executions
WHERE id = $1
`

func (q *Queries) GetChainExecution(ctx context.Context, id pgtype.UUID) (*ChainExecution, error) {
	row := q.db.QueryRow(ctx, GetChainExecution, id)
	var i ChainExecution
	err := row.Scan(
		&i.ID,
		&i.ChainType,
		&i.UserID,
		&i.Input,
		&i.Output,
		&i.Steps,
		&i.ExecutionTimeMs,
		&i.TokensUsed,
		&i.Success,
		&i.ErrorMessage,
		&i.CreatedAt,
		&i.Metadata,
	)
	return &i, err
}

const GetChainExecutionStats = `-- name: GetChainExecutionStats :one
SELECT
    chain_type,
    COUNT(*) as total_executions,
    COUNT(*) FILTER (WHERE success = true) as successful_executions,
    AVG(execution_time_ms) as avg_execution_time_ms,
    SUM(tokens_used) as total_tokens_used,
    MAX(created_at) as last_execution
FROM chain_executions
WHERE user_id = $1
  AND (chain_type = $2 OR $2 IS NULL)
  AND created_at >= $3
GROUP BY chain_type
`

type GetChainExecutionStatsParams struct {
	UserID    string    `json:"user_id"`
	ChainType string    `json:"chain_type"`
	CreatedAt time.Time `json:"created_at"`
}

type GetChainExecutionStatsRow struct {
	ChainType            string      `json:"chain_type"`
	TotalExecutions      int64       `json:"total_executions"`
	SuccessfulExecutions int64       `json:"successful_executions"`
	AvgExecutionTimeMs   float64     `json:"avg_execution_time_ms"`
	TotalTokensUsed      int64       `json:"total_tokens_used"`
	LastExecution        interface{} `json:"last_execution"`
}

func (q *Queries) GetChainExecutionStats(ctx context.Context, arg GetChainExecutionStatsParams) (*GetChainExecutionStatsRow, error) {
	row := q.db.QueryRow(ctx, GetChainExecutionStats, arg.UserID, arg.ChainType, arg.CreatedAt)
	var i GetChainExecutionStatsRow
	err := row.Scan(
		&i.ChainType,
		&i.TotalExecutions,
		&i.SuccessfulExecutions,
		&i.AvgExecutionTimeMs,
		&i.TotalTokensUsed,
		&i.LastExecution,
	)
	return &i, err
}

const GetChainExecutionsByUser = `-- name: GetChainExecutionsByUser :many
SELECT id, chain_type, user_id, input, output, steps, execution_time_ms, tokens_used, success, error_message, created_at, metadata FROM chain_executions
WHERE user_id = $1
  AND (chain_type = $2 OR $2 IS NULL)
ORDER BY created_at DESC
LIMIT $3 OFFSET $4
`

type GetChainExecutionsByUserParams struct {
	UserID    string `json:"user_id"`
	ChainType string `json:"chain_type"`
	Limit     int32  `json:"limit"`
	Offset    int32  `json:"offset"`
}

func (q *Queries) GetChainExecutionsByUser(ctx context.Context, arg GetChainExecutionsByUserParams) ([]*ChainExecution, error) {
	rows, err := q.db.Query(ctx, GetChainExecutionsByUser,
		arg.UserID,
		arg.ChainType,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ChainExecution{}
	for rows.Next() {
		var i ChainExecution
		if err := rows.Scan(
			&i.ID,
			&i.ChainType,
			&i.UserID,
			&i.Input,
			&i.Output,
			&i.Steps,
			&i.ExecutionTimeMs,
			&i.TokensUsed,
			&i.Success,
			&i.ErrorMessage,
			&i.CreatedAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetExecutionTrends = `-- name: GetExecutionTrends :many
SELECT
    DATE_TRUNC('day', ae.created_at) as execution_date,
    'agent' as execution_type,
    ae.agent_type as type_name,
    COUNT(*) as execution_count,
    COUNT(*) FILTER (WHERE ae.success = true) as successful_count,
    AVG(ae.execution_time_ms) as avg_execution_time_ms
FROM agent_executions ae
WHERE ae.user_id = $1 AND ae.created_at >= $2
GROUP BY DATE_TRUNC('day', ae.created_at), ae.agent_type
UNION ALL
SELECT
    DATE_TRUNC('day', ce.created_at) as execution_date,
    'chain' as execution_type,
    ce.chain_type as type_name,
    COUNT(*) as execution_count,
    COUNT(*) FILTER (WHERE ce.success = true) as successful_count,
    AVG(ce.execution_time_ms) as avg_execution_time_ms
FROM chain_executions ce
WHERE ce.user_id = $1 AND ce.created_at >= $2
GROUP BY DATE_TRUNC('day', ce.created_at), ce.chain_type
ORDER BY execution_date DESC, execution_type, type_name
`

type GetExecutionTrendsParams struct {
	UserID    string    `json:"user_id"`
	CreatedAt time.Time `json:"created_at"`
}

type GetExecutionTrendsRow struct {
	ExecutionDate      pgtype.Interval `json:"execution_date"`
	ExecutionType      string          `json:"execution_type"`
	TypeName           string          `json:"type_name"`
	ExecutionCount     int64           `json:"execution_count"`
	SuccessfulCount    int64           `json:"successful_count"`
	AvgExecutionTimeMs float64         `json:"avg_execution_time_ms"`
}

func (q *Queries) GetExecutionTrends(ctx context.Context, arg GetExecutionTrendsParams) ([]*GetExecutionTrendsRow, error) {
	rows, err := q.db.Query(ctx, GetExecutionTrends, arg.UserID, arg.CreatedAt)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetExecutionTrendsRow{}
	for rows.Next() {
		var i GetExecutionTrendsRow
		if err := rows.Scan(
			&i.ExecutionDate,
			&i.ExecutionType,
			&i.TypeName,
			&i.ExecutionCount,
			&i.SuccessfulCount,
			&i.AvgExecutionTimeMs,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRecentAgentExecutions = `-- name: GetRecentAgentExecutions :many
SELECT id, agent_type, user_id, query, response, steps, execution_time_ms, success, error_message, created_at, metadata FROM agent_executions
WHERE user_id = $1
ORDER BY created_at DESC
LIMIT $2
`

type GetRecentAgentExecutionsParams struct {
	UserID string `json:"user_id"`
	Limit  int32  `json:"limit"`
}

func (q *Queries) GetRecentAgentExecutions(ctx context.Context, arg GetRecentAgentExecutionsParams) ([]*AgentExecution, error) {
	rows, err := q.db.Query(ctx, GetRecentAgentExecutions, arg.UserID, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*AgentExecution{}
	for rows.Next() {
		var i AgentExecution
		if err := rows.Scan(
			&i.ID,
			&i.AgentType,
			&i.UserID,
			&i.Query,
			&i.Response,
			&i.Steps,
			&i.ExecutionTimeMs,
			&i.Success,
			&i.ErrorMessage,
			&i.CreatedAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRecentChainExecutions = `-- name: GetRecentChainExecutions :many
SELECT id, chain_type, user_id, input, output, steps, execution_time_ms, tokens_used, success, error_message, created_at, metadata FROM chain_executions
WHERE user_id = $1
ORDER BY created_at DESC
LIMIT $2
`

type GetRecentChainExecutionsParams struct {
	UserID string `json:"user_id"`
	Limit  int32  `json:"limit"`
}

func (q *Queries) GetRecentChainExecutions(ctx context.Context, arg GetRecentChainExecutionsParams) ([]*ChainExecution, error) {
	rows, err := q.db.Query(ctx, GetRecentChainExecutions, arg.UserID, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ChainExecution{}
	for rows.Next() {
		var i ChainExecution
		if err := rows.Scan(
			&i.ID,
			&i.ChainType,
			&i.UserID,
			&i.Input,
			&i.Output,
			&i.Steps,
			&i.ExecutionTimeMs,
			&i.TokensUsed,
			&i.Success,
			&i.ErrorMessage,
			&i.CreatedAt,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetUserExecutionSummary = `-- name: GetUserExecutionSummary :one

SELECT
    (SELECT COUNT(*) FROM agent_executions ae WHERE ae.user_id = $1 AND ae.created_at >= $2) as agent_executions,
    (SELECT COUNT(*) FROM chain_executions ce WHERE ce.user_id = $1 AND ce.created_at >= $2) as chain_executions,
    (SELECT AVG(ae.execution_time_ms) FROM agent_executions ae WHERE ae.user_id = $1 AND ae.created_at >= $2) as avg_agent_time_ms,
    (SELECT AVG(ce.execution_time_ms) FROM chain_executions ce WHERE ce.user_id = $1 AND ce.created_at >= $2) as avg_chain_time_ms,
    (SELECT SUM(ce.tokens_used) FROM chain_executions ce WHERE ce.user_id = $1 AND ce.created_at >= $2) as total_tokens_used
`

type GetUserExecutionSummaryParams struct {
	UserID    string    `json:"user_id"`
	CreatedAt time.Time `json:"created_at"`
}

type GetUserExecutionSummaryRow struct {
	AgentExecutions int64   `json:"agent_executions"`
	ChainExecutions int64   `json:"chain_executions"`
	AvgAgentTimeMs  float64 `json:"avg_agent_time_ms"`
	AvgChainTimeMs  float64 `json:"avg_chain_time_ms"`
	TotalTokensUsed int64   `json:"total_tokens_used"`
}

// Combined analytics queries
func (q *Queries) GetUserExecutionSummary(ctx context.Context, arg GetUserExecutionSummaryParams) (*GetUserExecutionSummaryRow, error) {
	row := q.db.QueryRow(ctx, GetUserExecutionSummary, arg.UserID, arg.CreatedAt)
	var i GetUserExecutionSummaryRow
	err := row.Scan(
		&i.AgentExecutions,
		&i.ChainExecutions,
		&i.AvgAgentTimeMs,
		&i.AvgChainTimeMs,
		&i.TotalTokensUsed,
	)
	return &i, err
}
