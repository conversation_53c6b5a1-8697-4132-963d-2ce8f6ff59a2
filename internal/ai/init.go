package ai

import (
	"log/slog"

	"github.com/koopa0/assistant-go/internal/ai/claude"
	"github.com/koopa0/assistant-go/internal/ai/gemini"
)

// RegisterProviders registers all available AI providers with the factory
func RegisterProviders(factory *Factory) {
	// Register Claude provider
	factory.RegisterProvider("claude", func(config ProviderConfig, logger *slog.Logger) (Provider, error) {
		return claude.NewClient(config, logger)
	})

	// Register Gemini provider
	factory.RegisterProvider("gemini", func(config ProviderConfig, logger *slog.Logger) (Provider, error) {
		return gemini.NewClient(config, logger)
	})
}
