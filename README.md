# GoAssistant

An intelligent AI-powered personal and development assistant built with Go, designed to enhance productivity through deep integration with modern development tools and AI capabilities.

## Overview

GoAssistant is a comprehensive assistant that combines cutting-edge AI models with practical development tools. It provides both CLI and web interfaces for seamless interaction, whether you're coding, managing infrastructure, or seeking general assistance.

## Key Features

### 🤖 AI Integration
- **Dual AI Support**: Seamlessly switch between Claude and Gemini models
- **Context-Aware Responses**: Maintains conversation history for coherent interactions
- **RAG Capabilities**: Enhanced responses through Retrieval-Augmented Generation
- **Agent Framework**: LangChain integration for complex task automation

### 🔍 Intelligent Search
- **Advanced Web Search**: Integrated SearXNG instance with enhanced capabilities
- **Semantic Search**: AI-powered search result ranking and filtering
- **Multi-Source Aggregation**: Combines results from multiple search engines

### 💻 Go Development Assistant
- **Code Analysis**: Deep static analysis with AST parsing
- **Performance Profiling**: Real-time CPU and memory profiling with pprof
- **Distributed Tracing**: Execution flow visualization and bottleneck detection
- **Runtime Monitoring**: Live metrics and resource usage tracking
- **Debugging Support**: Integrated debugging capabilities

### 🗄️ Database Expertise
- **PostgreSQL Mastery**: Advanced PostgreSQL operations and optimizations
- **Query Optimization**: AI-assisted query analysis and improvement
- **Schema Management**: Intelligent migration and schema evolution
- **Performance Tuning**: Database performance analysis and recommendations

### ☸️ Infrastructure Management
- **Kubernetes Control**: Native K8s API integration for cluster management
- **Resource Management**: Quota monitoring and optimization
- **Docker Operations**: Container lifecycle management and monitoring
- **Deployment Automation**: Streamlined deployment workflows

### ☁️ Cloud Integration
- **Cloudflare Services**: Deep integration with Cloudflare ecosystem
   - Tunnel management for secure connections
   - R2 storage operations
   - Pages deployment
   - Domain and DNS management

### 📊 Observability
- **Comprehensive Monitoring**: OpenTelemetry, Prometheus, Loki, and Jaeger integration
- **Real-time Metrics**: System and application performance metrics
- **Distributed Tracing**: Request flow visualization across components
- **Log Aggregation**: Centralized logging with structured output

## Use Cases

- **Development Productivity**: Accelerate Go development with intelligent code assistance
- **Infrastructure Management**: Simplify Kubernetes and Docker operations
- **Database Operations**: Optimize PostgreSQL performance and queries
- **Research and Analysis**: Leverage AI for complex research tasks
- **Automation**: Build custom agents for repetitive tasks
- **Learning Assistant**: Deep dive into Go internals and best practices

## Technology Stack

- **Language**: Go 1.24+
- **Database**: PostgreSQL 15+ with pgvector extension
- **AI Models**: Claude (Anthropic) and Gemini (Google)
- **Container Orchestration**: Kubernetes via Kind for local development
- **Search**: SearXNG for privacy-focused web search
- **UI Framework**: Templ + HTMX with Material Design 3
- **Observability**: OpenTelemetry, Prometheus, Loki, Jaeger

## Installation

### Prerequisites
- Go 1.24 or higher
- PostgreSQL 15+ with pgvector extension
- Docker and Kind (for Kubernetes features)
- Git

### Quick Start

```bash
# Clone the repository
git clone https://github.com/yourusername/goassistant.git
cd goassistant

# Setup development environment
make setup

# Configure environment
cp .env.example .env
# Edit .env with your API keys and configuration

# Run database migrations
make migrate

# Start the application
make run
```

## Configuration

GoAssistant uses environment-based configuration following the 12-factor app methodology. See `.env.example` for all available options.

### Required Configuration
- `CLAUDE_API_KEY` or `GEMINI_API_KEY` (at least one AI provider)
- `DATABASE_URL` (PostgreSQL connection string)

### Optional Services
- Cloudflare API credentials
- Kubernetes cluster configuration
- Observability endpoints

## Usage

### CLI Mode
```bash
# Interactive mode
goassistant cli

# Direct command
goassistant ask "Explain Go's memory model"
```

### Web Interface
```bash
# Start web server
goassistant serve

# Access at http://localhost:8080
```

## Documentation

- [Architecture Overview](docs/ARCHITECTURE.md)
- [Development Guide](docs/DEVELOPMENT.md)
- [API Reference](docs/API.md)
- [Configuration Guide](docs/CONFIGURATION.md)
- [Deployment Guide](docs/DEPLOYMENT.md)

## Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Process
1. Fork the repository
2. Create a feature branch
3. Make your changes following our coding standards
4. Add tests for new functionality
5. Submit a pull request

## Roadmap

See our [project board](https://github.com/yourusername/goassistant/projects) for current progress.

### Current Focus
- Core architecture implementation
- Basic AI integration
- PostgreSQL tooling

### Upcoming Features
- MCP (Model Context Protocol) support
- Enhanced RAG capabilities
- Plugin system for extensibility

## Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/yourusername/goassistant/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/goassistant/discussions)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with the principles of idiomatic Go
- Inspired by the need for a truly integrated development assistant
- Special thanks to the Go community and all contributors