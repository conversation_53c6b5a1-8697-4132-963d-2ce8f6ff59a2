# GoAssistant Architecture Document

## Executive Summary

GoAssistant is designed as a monolithic application with clear modular boundaries, built using idiomatic Go patterns without external web frameworks. The architecture prioritizes simplicity, maintainability, and performance while providing extensive integration capabilities.

## System Architecture

### High-Level Architecture

The system follows a layered architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────┐
│             Presentation Layer                  │
│         (CLI / Web UI / WebSocket)              │
├─────────────────────────────────────────────────┤
│             Application Layer                   │
│    (Assistant Core / Tool Orchestration)        │
├─────────────────────────────────────────────────┤
│              Integration Layer                  │
│  (AI Providers / External Services / Tools)     │
├─────────────────────────────────────────────────┤
│               Data Layer                        │
│        (PostgreSQL / File Storage)              │
└─────────────────────────────────────────────────┘
```

### Core Components

#### 1. Assistant Core
- **Purpose**: Central orchestration and routing logic
- **Responsibilities**:
    - Request processing and validation
    - Tool selection and execution
    - Response generation and formatting
    - Context management
    - Error handling and recovery

#### 2. AI Provider Integration
- **Supported Providers**: <PERSON> (Anthropic), Gemini (Google)
- **Capabilities**:
    - Text generation and completion
    - Embedding generation for RAG
    - Model switching at runtime
    - Fallback mechanisms
    - Token usage tracking

#### 3. Tool System
- **Architecture**: Plugin-based tool registration
- **Built-in Tools**:
    - Web Search (SearXNG integration)
    - Go Development Suite
    - PostgreSQL Operations
    - Kubernetes Management
    - Docker Control
    - Cloudflare Services
    - LangChain Agents

#### 4. Storage Layer
- **Primary Database**: PostgreSQL 15+
- **Extensions Required**:
    - pgvector for embeddings
    - pg_stat_statements for query analysis
- **Data Models**:
    - Conversations and messages
    - Tool execution history
    - User preferences
    - Embeddings for RAG
    - Cached search results

## Feature Specifications

### Web Search Integration

**Requirements**:
- Self-hosted SearXNG instance via Docker/Kind
- Result aggregation from multiple search engines
- Privacy-focused configuration
- Semantic ranking of results
- Caching for performance

**Integration Points**:
- HTTP API communication
- Result parsing and normalization
- Embedding generation for semantic search
- Cache management in PostgreSQL

### Go Development Assistant

**Core Features**:
1. **Static Analysis**
    - AST parsing and analysis
    - Code quality metrics
    - Dependency analysis
    - Security vulnerability scanning

2. **Performance Profiling**
    - CPU profiling via pprof
    - Memory profiling and leak detection
    - Goroutine analysis
    - Block and mutex profiling

3. **Runtime Monitoring**
    - Real-time metrics collection
    - Resource usage tracking
    - GC statistics
    - HTTP server metrics

4. **Tracing and Debugging**
    - Distributed tracing with OpenTelemetry
    - Execution flow visualization
    - Debug symbol integration
    - Stack trace analysis

### PostgreSQL Integration

**Advanced Features**:
- Query plan analysis and optimization
- Index recommendation engine
- Performance metric collection
- Automatic EXPLAIN ANALYZE
- Connection pool monitoring
- Lock analysis and deadlock detection
- Backup and restore automation

**pgx Driver Utilization**:
- Prepared statement caching
- COPY protocol for bulk operations
- Listen/Notify for real-time events
- Custom type support
- Connection pool configuration

### Kubernetes Integration

**Management Capabilities**:
- Resource CRUD operations
- Quota management and monitoring
- Pod log streaming
- Event watching
- Metric collection via metrics-server
- Custom Resource Definition support
- Helm chart management

**Implementation Requirements**:
- client-go library usage
- In-cluster and out-of-cluster support
- RBAC-aware operations
- Multi-cluster support

### Docker Integration

**Features**:
- Container lifecycle management
- Image management and scanning
- Volume and network operations
- Resource monitoring
- Log streaming
- Docker Compose support
- Build automation

**Technical Requirements**:
- Docker SDK for Go
- Unix socket or TCP connection
- Event streaming
- Stats collection

### Cloudflare Integration

**Services to Integrate**:
1. **Cloudflare Tunnel**
    - Tunnel creation and management
    - Configuration updates
    - Health monitoring

2. **R2 Storage**
    - Object CRUD operations
    - Presigned URL generation
    - Multipart upload support

3. **Pages**
    - Deployment automation
    - Build status monitoring
    - Environment variable management

4. **DNS Management**
    - Record CRUD operations
    - Bulk updates
    - DNSSEC configuration

### Agent Framework (LangChain)

**Capabilities**:
- Tool chain execution
- Memory management
- Custom agent creation
- Workflow automation
- Multi-step reasoning

**Integration with langchaingo**:
- LLM abstraction
- Tool interface implementation
- Chain composition
- Memory backends

## Technical Requirements

### Core Technology Stack

**Go Version**: 1.24+ (required for latest stdlib features)

**Standard Library Usage**:
- **HTTP Server**: Pure `net/http` - no web frameworks
- **Logging**: `log/slog` for structured logging
- **Error Handling**: Error wrapping with `fmt.Errorf` and `%w` verb
- **Context**: Extensive use of `context.Context` for cancellation and values

**Essential Libraries**:
- **PostgreSQL Driver**: [`github.com/jackc/pgx/v5`](https://github.com/jackc/pgx)
- **LangChain Go**: [`github.com/tmc/langchaingo`](https://github.com/tmc/langchaingo)
- **Templ**: For type-safe HTML templates
- **HTMX**: For progressive enhancement (loaded from CDN)

**Development Tools**:
- **sqlc**: For type-safe SQL query generation
- **golangci-lint**: For code quality
- **go generate**: For code generation tasks

### Go Development Standards

1. **HTTP Server Implementation**
    - Use Go 1.24+ enhanced `net/http` package
    - Custom middleware chain implementation
    - No Gin, Echo, or other web frameworks
    - ServeMux with method-based routing
    - Proper context propagation

2. **Error Handling Pattern**
    - Use error wrapping: `fmt.Errorf("operation failed: %w", err)`
    - Create semantic error types
    - Check errors explicitly
    - Add context at each layer

3. **Logging with slog**
    - Structured logging throughout
    - Context-aware loggers
    - Log levels: Debug, Info, Warn, Error
    - Request ID propagation

4. **Code Organization**
    - Package by feature, not by layer
    - Accept interfaces, return structs
    - Composition over inheritance
    - Minimal external dependencies

5. **Database Access with pgx**
    - Use pgx native interface (not database/sql)
    - Connection pooling with pgxpool
    - Prepared statements
    - COPY protocol for bulk operations
    - Listen/Notify for real-time events

6. **Web Development Approach**
    - Server-side rendering with Templ
    - Progressive enhancement with HTMX
    - WebSocket for real-time features
    - No client-side JavaScript frameworks
    - Material Design 3 components from [templui.io](https://templui.io)

### Implementation Guidelines

#### HTTP Server Structure
- **Router**: Use `http.ServeMux` with pattern matching
- **Middleware**: Chain pattern using `func(http.Handler) http.Handler`
- **Handlers**: Method receivers on service structs
- **Request/Response**: JSON encoding/decoding with `encoding/json`
- **Static Files**: `http.FileServer` with embed for production

#### Error Handling Strategy
- **Wrapping**: Always use `fmt.Errorf` with `%w` for error context
- **Custom Errors**: Define domain-specific error types
- **HTTP Errors**: Structured error responses with appropriate status codes
- **Logging**: Log errors with full context using slog

#### Database Operations
- **Queries**: Generated by sqlc from SQL files
- **Transactions**: Explicit transaction management
- **Migrations**: SQL-based migrations with up/down support
- **Connection**: pgxpool for connection pooling
- **Monitoring**: pg_stat_statements integration

#### LangChain Integration
- **Agents**: Use langchaingo agent framework
- **Tools**: Implement langchain Tool interface
- **Memory**: PostgreSQL-backed conversation memory
- **Chains**: Composable chain patterns
- **Callbacks**: Monitoring and debugging hooks

### Web Interface Requirements

**Technology Stack**:
- Templ for type-safe templates
- HTMX for dynamic interactions
- WebSocket for real-time features
- Material Design 3 components
- No JavaScript frameworks

**Features**:
- Server-side rendering
- Progressive enhancement
- Real-time updates
- Responsive design
- Accessibility compliance

### Observability Stack

1. **Metrics (Prometheus)**
    - Application metrics
    - Business metrics
    - SLI/SLO tracking
    - Custom dashboards

2. **Logging (Loki)**
    - Structured logging with slog
    - Log aggregation
    - Query capabilities
    - Alert integration

3. **Tracing (Jaeger)**
    - Distributed tracing
    - Latency analysis
    - Dependency mapping
    - Performance bottleneck identification

4. **OpenTelemetry Integration**
    - Unified instrumentation
    - Vendor-agnostic design
    - Auto-instrumentation where possible

## Deployment Architecture

### Local Development (Kind)

**Cluster Configuration**:
- Single control plane node
- Multiple worker nodes
- Port forwarding for services
- Volume mounts for development

**Required Services**:
- PostgreSQL with pgvector
- SearXNG
- Observability stack
- GoAssistant deployment

### Production Considerations

**Scalability**:
- Horizontal scaling capability
- Stateless application design
- Database connection pooling
- Cache layer implementation

**Security**:
- API key management
- RBAC implementation
- Network policies
- Secret rotation

**High Availability**:
- Health checks
- Graceful shutdown
- Circuit breakers
- Retry mechanisms

## Data Flow and Integration Points

### Request Processing Flow

1. **Input Reception**
    - CLI arguments parsing
    - HTTP request handling
    - WebSocket message processing

2. **Request Routing**
    - Tool selection logic
    - Priority determination
    - Load balancing

3. **Execution Pipeline**
    - Pre-processing
    - Tool execution
    - Post-processing
    - Response formatting

4. **State Management**
    - Conversation tracking
    - Context preservation
    - Session management

### External Service Integration

**API Communication**:
- HTTP/2 support
- Connection pooling
- Retry logic
- Circuit breakers
- Rate limiting

**Authentication Methods**:
- API key management
- OAuth2 where applicable
- Service account handling
- Token refresh logic

## Security Architecture

### Authentication & Authorization
- JWT-based authentication
- Role-based access control
- API key management
- Session handling

### Data Protection
- Encryption at rest
- TLS for all communications
- Sensitive data masking
- Audit logging

### Compliance Considerations
- GDPR compliance for user data
- API usage tracking
- Data retention policies
- Right to deletion

## Performance Requirements

### Response Time Targets
- CLI commands: < 100ms overhead
- Web UI interactions: < 200ms
- AI responses: < 5s for simple queries
- Search operations: < 2s

### Resource Utilization
- Memory: < 500MB baseline
- CPU: Efficient goroutine usage
- Database connections: Pooled and limited
- Network: Optimized payload sizes

## Testing Strategy

### Test Categories
1. **Unit Tests**
    - Business logic validation
    - Error handling verification
    - Interface contract testing

2. **Integration Tests**
    - Database operations
    - External API interactions
    - Tool execution flows

3. **End-to-End Tests**
    - User journey validation
    - Performance benchmarks
    - Load testing

### Quality Metrics
- Code coverage > 80%
- All critical paths tested
- Performance regression detection
- Security vulnerability scanning

## Go Best Practices References

This project follows established Go best practices as documented in:

1. **[Effective Go](https://go.dev/doc/effective_go)** - Core Go idioms
2. **[Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)** - Google's Go style guide
3. **[Standard Package Layout](https://go.dev/doc/modules/layout)** - Package organization
4. **[Go Proverbs](https://go-proverbs.github.io/)** - Guiding principles
5. **Standard Library Examples** - Learn from Go's own source code

Key principles applied:
- "Accept interfaces, return structs"
- "The bigger the interface, the weaker the abstraction"
- "Make the zero value useful"
- "Errors are values"
- "Don't panic"
- "Concurrency is not parallelism"
- "Share memory by communicating"

## Migration and Upgrade Strategy

### Database Migrations
- Version-controlled migrations
- Rollback capabilities
- Zero-downtime migrations
- Data validation

### Application Updates
- Backward compatibility
- Feature flags
- Gradual rollout
- Health monitoring

## Future Extensibility

### Plugin System Design
- Dynamic tool loading
- Version compatibility
- Dependency management
- Security sandboxing

### API Gateway Potential
- Rate limiting
- API versioning
- Request routing
- Analytics collection

### Multi-tenancy Considerations
- User isolation
- Resource quotas
- Billing integration
- Custom configurations